/* Our Planet Page - Space Theme */
.planet-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding-top: 70px;
}

/* Hero Section */
.planet-hero {
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.planet-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.planet-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

.planet-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.planet-hero-highlight {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.planet-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #b3b3b3;
  max-width: 600px;
  margin: 0 auto;
}

/* Content Section */
.planet-content {
  padding: 2rem 0 4rem;
  position: relative;
}

.planet-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.05), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.03), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.planet-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Controls */
.planet-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.planet-search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.planet-search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem 0.75rem 3rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.planet-search-input:focus {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.planet-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.planet-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
}

.planet-category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.planet-category-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.planet-category-btn:hover,
.planet-category-btn.active {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
}

/* Featured Content */
.planet-featured {
  margin-bottom: 3rem;
}

.planet-featured-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  transition: all 0.3s ease;
}

.planet-featured-content:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.planet-featured-image {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.planet-featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.planet-featured-content:hover .planet-featured-image img {
  transform: scale(1.05);
}

.planet-featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.planet-category-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.planet-featured-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.planet-featured-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.planet-type-tag {
  background: rgba(79, 70, 229, 0.2);
  color: #a5b4fc;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.planet-read-time {
  color: #b3b3b3;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.planet-featured-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  line-height: 1.3;
}

.planet-featured-description {
  font-size: 1rem;
  color: #d1d5db;
  line-height: 1.6;
}

.planet-featured-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.planet-author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.planet-author-name {
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
}

.planet-publish-date {
  color: #b3b3b3;
  font-size: 0.8rem;
}

.planet-facts h4,
.planet-impact h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #a5b4fc;
}

.planet-facts-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.planet-fact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d1d5db;
  font-size: 0.9rem;
}

.planet-fact-item::before {
  content: '🌍';
  font-size: 0.8rem;
}

.planet-impact-text {
  color: #d1d5db;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
}

/* Content Grid */
.planet-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.planet-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.planet-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.planet-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.planet-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.planet-card:hover .planet-card-image img {
  transform: scale(1.05);
}

.planet-card-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.planet-card-type {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.planet-card-content {
  padding: 1.5rem;
}

.planet-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #b3b3b3;
}

.planet-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: white;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.planet-card-description {
  font-size: 0.9rem;
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.planet-card-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.planet-card-author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.planet-card-author-name {
  font-size: 0.85rem;
  color: #d1d5db;
  font-weight: 500;
}

.planet-card-facts h5 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #a5b4fc;
}

.planet-card-facts-preview {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.planet-fact-preview {
  font-size: 0.8rem;
  color: #d1d5db;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.planet-fact-preview::before {
  content: '•';
  color: #10b981;
  font-weight: bold;
}

.planet-card-impact {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.planet-impact-label {
  font-size: 0.8rem;
  color: #a5b4fc;
  font-weight: 600;
}

.planet-impact-preview {
  font-size: 0.8rem;
  color: #d1d5db;
  line-height: 1.4;
  font-style: italic;
}

/* No Results */
.planet-no-results {
  text-align: center;
  padding: 3rem;
  color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .planet-hero-title {
    font-size: 2.5rem;
  }

  .planet-hero-subtitle {
    font-size: 1.1rem;
  }

  .planet-container {
    padding: 0 1rem;
  }

  .planet-featured-content {
    grid-template-columns: 1fr;
  }

  .planet-featured-image {
    height: 250px;
  }

  .planet-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .planet-category-filters {
    gap: 0.5rem;
  }

  .planet-category-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .planet-hero {
    padding: 2rem 0;
  }

  .planet-hero-title {
    font-size: 2rem;
  }

  .planet-featured-info {
    padding: 1.5rem;
  }

  .planet-card-content {
    padding: 1rem;
  }

  .planet-search-container {
    max-width: 100%;
  }
}

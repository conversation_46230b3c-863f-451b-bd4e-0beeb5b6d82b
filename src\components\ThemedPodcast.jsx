import React, { useState } from 'react';
import './ThemedPodcast.css';

const ThemedPodcast = () => {
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);

  // Space-themed podcast data matching the design
  const podcastData = [
    {
      id: 1,
      title: "Today's Technology with <PERSON>",
      description: "On this podcast, I talk about what happened in technology.",
      thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
      duration: "45:32",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 2,
      title: "Everyday Plant: The Plant and It Lifes",
      description: "Let's get to know more about plant and it lifes!",
      thumbnail: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop&crop=center",
      duration: "38:15",
      publishedAt: "4 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Sarah Green",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 3,
      title: "Boy & Joy: Travel Through Wildlife",
      description: "With Boy, you will know the stories in the wildlife.",
      thumbnail: "https://images.unsplash.com/photo-1549366021-9f761d040a94?w=400&h=400&fit=crop&crop=center",
      duration: "52:18",
      publishedAt: "1 week ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Boy Wilson",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 4,
      title: "Spaceronauts: Space and Universe Updates",
      description: "Spaceronauts, where we can know the update in space.",
      thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop&crop=center",
      duration: "42:30",
      publishedAt: "3 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Mike Chen",
        avatar: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 5,
      title: "Sonya Adventure: The Life of Traveler",
      description: "With Sonya, you will know the life of a backpacker.",
      thumbnail: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=400&fit=crop&crop=center",
      duration: "35:45",
      publishedAt: "5 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Sonya Martinez",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 6,
      title: "Earth Now: What Happens With Earth?",
      description: "Earth Now, where you can get updates of our planet.",
      thumbnail: "https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=400&h=400&fit=crop&crop=center",
      duration: "48:12",
      publishedAt: "6 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Emma Stone",
        avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 7,
      title: "Whynauts: What's the Life of Astronauts?",
      description: "Let's dig deeper with my friends about the life of astronauts.",
      thumbnail: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=400&fit=crop&crop=center",
      duration: "55:20",
      publishedAt: "1 week ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Commander Lisa Park",
        avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 8,
      title: "Extraterrestrial Life: Is It Exists?",
      description: "We'll dig deeper to get to know the truth of alien existence.",
      thumbnail: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=400&fit=crop&crop=center",
      duration: "41:15",
      publishedAt: "4 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Alex Turner",
        avatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=80&h=80&fit=crop&crop=center"
      }
    },
    {
      id: 9,
      title: "Teeech: A Journey With Technology",
      description: "Newest updates through the technology and stuffs.",
      thumbnail: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop&crop=center",
      duration: "39:30",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Tech Guru",
        avatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=80&h=80&fit=crop&crop=center"
      }
    }
  ];

  const handlePlayPause = (episodeId) => {
    setCurrentlyPlaying(currentlyPlaying === episodeId ? null : episodeId);
  };

  return (
    <section className="themed-podcast-section">
      <div className="themed-podcast-container">
        <div className="themed-podcast-header">
          <h2 className="themed-podcast-title">Our Podcasts</h2>
          <button className="themed-podcast-see-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        <div className="themed-podcast-grid">
          {podcastData.map((episode) => (
            <article key={episode.id} className="themed-podcast-card">
              <div className="themed-podcast-thumbnail">
                <img src={episode.thumbnail} alt={episode.title} />
                <button 
                  className="themed-play-button"
                  onClick={() => handlePlayPause(episode.id)}
                  aria-label={`${currentlyPlaying === episode.id ? 'Pause' : 'Play'} ${episode.title}`}
                >
                  <svg className="themed-play-icon" viewBox="0 0 24 24" fill="currentColor">
                    {currentlyPlaying === episode.id ? (
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    ) : (
                      <path d="M8 5v14l11-7z"/>
                    )}
                  </svg>
                </button>
              </div>

              <div className="themed-podcast-content">
                <h3 className="themed-podcast-card-title">{episode.title}</h3>
                <p className="themed-podcast-description">{episode.description}</p>
                
                <div className="themed-podcast-meta">
                  <div className="themed-host-info">
                    <img src={episode.host.avatar} alt={episode.host.name} className="themed-host-avatar" />
                    <span className="themed-host-name">{episode.host.name}</span>
                  </div>
                </div>
              </div>

              {currentlyPlaying === episode.id && (
                <div className="themed-audio-player-container">
                  <div className="themed-simple-audio-player">
                    <audio controls src={episode.audioUrl} style={{ width: '100%' }}>
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                </div>
              )}
            </article>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ThemedPodcast;

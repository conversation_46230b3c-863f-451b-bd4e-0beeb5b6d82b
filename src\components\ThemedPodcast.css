/* Themed Podcast Section - Matching Website Design */
.themed-podcast-section {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.themed-podcast-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.themed-podcast-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.themed-podcast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.themed-podcast-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.themed-podcast-see-all {
  background: transparent;
  border: none;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.themed-podcast-see-all:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.arrow {
  transition: transform 0.3s ease;
}

.themed-podcast-see-all:hover .arrow {
  transform: translateX(3px);
}

.themed-podcast-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.themed-podcast-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.themed-podcast-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.themed-podcast-thumbnail {
  position: relative;
  height: 160px;
  overflow: hidden;
  flex-shrink: 0;
}

.themed-podcast-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.themed-podcast-card:hover .themed-podcast-thumbnail img {
  transform: scale(1.05);
}

.themed-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(79, 70, 229, 0.9);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.themed-podcast-card:hover .themed-play-button {
  opacity: 1;
}

.themed-play-button:hover {
  background: rgba(79, 70, 229, 1);
  transform: translate(-50%, -50%) scale(1.1);
}

.themed-play-icon {
  width: 24px;
  height: 24px;
}

.themed-podcast-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.themed-podcast-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.themed-podcast-description {
  font-size: 0.9rem;
  color: #b3b3b3;
  margin: 0 0 1rem 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.themed-podcast-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.themed-host-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.themed-host-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.themed-host-name {
  font-size: 0.85rem;
  color: #d1d5db;
  font-weight: 500;
}

.themed-audio-player-container {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.themed-simple-audio-player audio {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.themed-simple-audio-player audio::-webkit-media-controls-panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.themed-simple-audio-player audio::-webkit-media-controls-play-button,
.themed-simple-audio-player audio::-webkit-media-controls-pause-button {
  background-color: rgba(79, 70, 229, 0.8);
  border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .themed-podcast-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .themed-podcast-section {
    padding: 4rem 0;
  }

  .themed-podcast-container {
    padding: 0 1rem;
  }

  .themed-podcast-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .themed-podcast-title {
    font-size: 2rem;
  }

  .themed-podcast-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .themed-podcast-thumbnail {
    height: 180px;
  }

  .themed-play-button {
    width: 50px;
    height: 50px;
  }

  .themed-play-icon {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .themed-podcast-title {
    font-size: 1.75rem;
  }

  .themed-podcast-content {
    padding: 1rem;
  }

  .themed-podcast-thumbnail {
    height: 160px;
  }

  .themed-host-avatar {
    width: 28px;
    height: 28px;
  }

  .themed-host-name {
    font-size: 0.8rem;
  }
}

import React, { useState } from 'react';
import './HealthSciencePage.css';

const HealthSciencePage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const healthCategories = ['All', 'Space Medicine', 'Microgravity', 'Radiation', 'Psychology', 'Life Sciences'];

  const healthContent = [
    {
      id: 1,
      title: "Microgravity Effects on Human Bone Density",
      description: "Comprehensive study on how prolonged exposure to microgravity affects astronaut bone health and potential countermeasures.",
      image: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=800&h=400&fit=crop&crop=center",
      category: "Microgravity",
      studyType: "Clinical Research",
      institution: "NASA Johnson Space Center",
      duration: "18-month study",
      participants: "24 astronauts",
      featured: true,
      keyFindings: [
        "1-2% bone loss per month in space",
        "Hip and spine most affected areas",
        "Exercise protocols reduce loss by 50%"
      ],
      implications: "Critical for long-duration Mars missions",
      author: "<PERSON>. <PERSON>",
      authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center",
      publishedAt: "1 week ago"
    },
    {
      id: 2,
      title: "Cosmic Radiation Shielding Technologies",
      description: "Advanced materials and techniques for protecting astronauts from harmful cosmic radiation during deep space missions.",
      image: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=800&h=400&fit=crop&crop=center",
      category: "Radiation",
      studyType: "Materials Science",
      institution: "ESA Space Medicine",
      duration: "3-year development",
      participants: "Multi-institutional",
      featured: false,
      keyFindings: [
        "Polyethylene reduces radiation by 30%",
        "Magnetic shielding shows promise",
        "Water-based shields most effective"
      ],
      implications: "Essential for Mars mission safety",
      author: "Prof. Michael Chen",
      authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center",
      publishedAt: "3 days ago"
    },
    {
      id: 3,
      title: "Psychological Isolation in Long-Duration Missions",
      description: "Mental health challenges and coping strategies for astronauts during extended space missions and isolation.",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&h=400&fit=crop&crop=center",
      category: "Psychology",
      studyType: "Behavioral Study",
      institution: "Mars Desert Research Station",
      duration: "12-month simulation",
      participants: "48 crew members",
      featured: false,
      keyFindings: [
        "Social dynamics crucial for success",
        "Virtual reality reduces stress by 40%",
        "Regular Earth contact essential"
      ],
      implications: "Key factor in mission success rates",
      author: "Dr. Elena Rodriguez",
      authorAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=center",
      publishedAt: "5 days ago"
    },
    {
      id: 4,
      title: "Plant Growth in Microgravity Environments",
      description: "Investigating how plants adapt and grow in space conditions for sustainable food production during long missions.",
      image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=400&fit=crop&crop=center",
      category: "Life Sciences",
      studyType: "Botanical Research",
      institution: "ISS National Lab",
      duration: "Ongoing experiments",
      participants: "International teams",
      featured: false,
      keyFindings: [
        "Root growth patterns altered in space",
        "LED lighting optimizes growth rates",
        "Some crops yield 30% more in space"
      ],
      implications: "Enables sustainable space agriculture",
      author: "Dr. Plant Scientist",
      authorAvatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=40&h=40&fit=crop&crop=center",
      publishedAt: "1 week ago"
    },
    {
      id: 5,
      title: "Cardiovascular Changes in Zero Gravity",
      description: "How the human cardiovascular system adapts to weightlessness and the long-term health implications.",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=400&fit=crop&crop=center",
      category: "Space Medicine",
      studyType: "Medical Research",
      institution: "Baylor College of Medicine",
      duration: "5-year longitudinal study",
      participants: "156 astronauts",
      featured: false,
      keyFindings: [
        "Heart muscle mass decreases 20%",
        "Blood volume reduces significantly",
        "Exercise protocols maintain function"
      ],
      implications: "Critical for crew health monitoring",
      author: "Dr. Cardio Expert",
      authorAvatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=40&h=40&fit=crop&crop=center",
      publishedAt: "2 weeks ago"
    },
    {
      id: 6,
      title: "Sleep Patterns and Circadian Rhythms in Space",
      description: "Understanding how the absence of natural day-night cycles affects astronaut sleep quality and performance.",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop&crop=center",
      category: "Space Medicine",
      studyType: "Sleep Research",
      institution: "Harvard Sleep Medicine",
      duration: "2-year study",
      participants: "32 astronauts",
      featured: false,
      keyFindings: [
        "Sleep quality decreases 35% in space",
        "Artificial lighting helps maintain rhythms",
        "Melatonin supplements show benefits"
      ],
      implications: "Affects cognitive performance and safety",
      author: "Dr. Sleep Researcher",
      authorAvatar: "https://images.unsplash.com/photo-**********-94ddf0286df2?w=40&h=40&fit=crop&crop=center",
      publishedAt: "4 days ago"
    }
  ];

  const filteredContent = healthContent.filter(content => {
    const matchesCategory = selectedCategory === 'All' || content.category === selectedCategory;
    const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.studyType.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredContent = filteredContent.find(content => content.featured);
  const regularContent = filteredContent.filter(content => !content.featured);

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Space Medicine': return '#ef4444';
      case 'Microgravity': return '#8b5cf6';
      case 'Radiation': return '#f59e0b';
      case 'Psychology': return '#10b981';
      case 'Life Sciences': return '#3b82f6';
      default: return '#6b7280';
    }
  };

  return (
    <div className="health-page">
      {/* Hero Section */}
      <section className="health-hero">
        <div className="health-hero-container">
          <div className="health-hero-content">
            <h1 className="health-hero-title">
              Health & <span className="health-hero-highlight">Science</span>
            </h1>
            <p className="health-hero-subtitle">
              Explore cutting-edge research in space medicine, human adaptation to extreme environments, 
              and the scientific breakthroughs that keep astronauts healthy during their cosmic journeys.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="health-content">
        <div className="health-container">
          {/* Search and Filter Controls */}
          <div className="health-controls">
            <div className="health-search-container">
              <input
                type="text"
                placeholder="Search health & science research..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="health-search-input"
              />
              <svg className="health-search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>

            <div className="health-category-filters">
              {healthCategories.map(category => (
                <button
                  key={category}
                  className={`health-category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Research */}
          {featuredContent && (
            <div className="health-featured">
              <div className="health-featured-content">
                <div className="health-featured-image">
                  <img src={featuredContent.image} alt={featuredContent.title} />
                  <div className="health-featured-badge">Featured Research</div>
                  <div className="health-category-badge" style={{ backgroundColor: getCategoryColor(featuredContent.category) }}>
                    {featuredContent.category}
                  </div>
                </div>
                <div className="health-featured-info">
                  <div className="health-featured-meta">
                    <span className="health-study-type">{featuredContent.studyType}</span>
                    <span className="health-institution">{featuredContent.institution}</span>
                  </div>
                  <h2 className="health-featured-title">{featuredContent.title}</h2>
                  <p className="health-featured-description">{featuredContent.description}</p>
                  
                  <div className="health-study-details">
                    <div className="health-detail-item">
                      <span className="health-detail-label">Duration:</span>
                      <span className="health-detail-value">{featuredContent.duration}</span>
                    </div>
                    <div className="health-detail-item">
                      <span className="health-detail-label">Participants:</span>
                      <span className="health-detail-value">{featuredContent.participants}</span>
                    </div>
                  </div>

                  <div className="health-featured-author">
                    <img src={featuredContent.authorAvatar} alt={featuredContent.author} className="health-author-avatar" />
                    <div className="health-author-info">
                      <span className="health-author-name">{featuredContent.author}</span>
                      <span className="health-publish-date">{featuredContent.publishedAt}</span>
                    </div>
                  </div>

                  <div className="health-findings">
                    <h4>Key Findings</h4>
                    <ul className="health-findings-list">
                      {featuredContent.keyFindings.map((finding, index) => (
                        <li key={index} className="health-finding-item">{finding}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="health-implications">
                    <h4>Research Implications</h4>
                    <p className="health-implications-text">{featuredContent.implications}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Research Grid */}
          <div className="health-grid">
            {regularContent.map(content => (
              <article key={content.id} className="health-card">
                <div className="health-card-image">
                  <img src={content.image} alt={content.title} />
                  <div className="health-card-category" style={{ backgroundColor: getCategoryColor(content.category) }}>
                    {content.category}
                  </div>
                  <div className="health-card-type">{content.studyType}</div>
                </div>
                <div className="health-card-content">
                  <div className="health-card-meta">
                    <span className="health-card-institution">{content.institution}</span>
                    <span className="health-card-date">{content.publishedAt}</span>
                  </div>
                  <h3 className="health-card-title">{content.title}</h3>
                  <p className="health-card-description">{content.description}</p>
                  
                  <div className="health-card-details">
                    <div className="health-card-detail">
                      <span className="health-card-detail-label">Duration:</span>
                      <span className="health-card-detail-value">{content.duration}</span>
                    </div>
                    <div className="health-card-detail">
                      <span className="health-card-detail-label">Participants:</span>
                      <span className="health-card-detail-value">{content.participants}</span>
                    </div>
                  </div>

                  <div className="health-card-author">
                    <img src={content.authorAvatar} alt={content.author} className="health-card-author-avatar" />
                    <span className="health-card-author-name">{content.author}</span>
                  </div>

                  <div className="health-card-findings">
                    <h5>Key Findings</h5>
                    <div className="health-card-findings-preview">
                      {content.keyFindings.slice(0, 2).map((finding, index) => (
                        <div key={index} className="health-finding-preview">{finding}</div>
                      ))}
                    </div>
                  </div>

                  <div className="health-card-implications">
                    <span className="health-implications-label">Impact:</span>
                    <span className="health-implications-preview">{content.implications}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {filteredContent.length === 0 && (
            <div className="health-no-results">
              <p>No research found matching your search criteria.</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default HealthSciencePage;

import React, { useState } from 'react';
import './OurPlanetPage.css';

const OurPlanetPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const planetCategories = ['All', 'Climate', 'Ecosystems', 'Geology', 'Oceans', 'Atmosphere'];

  const planetContent = [
    {
      id: 1,
      title: "Earth's Magnetic Field: Our Invisible Shield",
      description: "Discover how Earth's magnetic field protects us from harmful solar radiation and cosmic particles, making life possible on our planet.",
      image: "https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=800&h=400&fit=crop&crop=center",
      category: "Atmosphere",
      type: "Research Article",
      author: "Dr. <PERSON>",
      authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center",
      publishedAt: "3 days ago",
      readTime: "8 min read",
      featured: true,
      facts: [
        "Extends 65,000 km into space",
        "Deflects 99% of solar wind",
        "Reverses every 200,000-300,000 years"
      ],
      impact: "Critical for maintaining atmosphere and protecting life"
    },
    {
      id: 2,
      title: "Amazon Rainforest: The Lungs of Our Planet",
      description: "Explore the vital role of the Amazon rainforest in global climate regulation and biodiversity conservation.",
      image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=400&fit=crop&crop=center",
      category: "Ecosystems",
      type: "Environmental Study",
      author: "Prof. Marcus Silva",
      authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center",
      publishedAt: "1 week ago",
      readTime: "12 min read",
      featured: false,
      facts: [
        "Produces 20% of world's oxygen",
        "Home to 10% of known species",
        "Stores 25% of terrestrial carbon"
      ],
      impact: "Essential for global climate stability"
    },
    {
      id: 3,
      title: "Deep Ocean Trenches: Earth's Final Frontier",
      description: "Journey into the deepest parts of our oceans and discover the extreme life forms that thrive in these alien environments.",
      image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&h=400&fit=crop&crop=center",
      category: "Oceans",
      type: "Exploration Report",
      author: "Dr. Sarah Chen",
      authorAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=center",
      publishedAt: "5 days ago",
      readTime: "10 min read",
      featured: false,
      facts: [
        "Deepest point: 11,034 meters",
        "Pressure: 1,100x surface pressure",
        "Temperature: 1-4°C year-round"
      ],
      impact: "Key to understanding life's limits and origins"
    },
    {
      id: 4,
      title: "Polar Ice Caps: Climate Change Indicators",
      description: "Understand how polar ice caps serve as crucial indicators of global climate change and their impact on sea levels.",
      image: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop&crop=center",
      category: "Climate",
      type: "Climate Analysis",
      author: "Dr. Arctic Johnson",
      authorAvatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=40&h=40&fit=crop&crop=center",
      publishedAt: "2 days ago",
      readTime: "9 min read",
      featured: false,
      facts: [
        "Arctic ice declining 13% per decade",
        "Contains 68.7% of fresh water",
        "Reflects 90% of solar radiation"
      ],
      impact: "Critical for global sea level and weather patterns"
    },
    {
      id: 5,
      title: "Volcanic Activity: Earth's Internal Engine",
      description: "Explore how volcanic activity shapes our planet's surface and influences global climate patterns.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop&crop=center",
      category: "Geology",
      type: "Geological Survey",
      author: "Prof. Maria Volcano",
      authorAvatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=40&h=40&fit=crop&crop=center",
      publishedAt: "1 week ago",
      readTime: "7 min read",
      featured: false,
      facts: [
        "1,500 active volcanoes worldwide",
        "Creates new land continuously",
        "Releases 200 million tons CO2 annually"
      ],
      impact: "Shapes continents and affects global climate"
    },
    {
      id: 6,
      title: "Coral Reef Ecosystems: Underwater Cities",
      description: "Dive into the complex ecosystems of coral reefs and their crucial role in marine biodiversity.",
      image: "https://images.unsplash.com/photo-1583212292454-1fe6229603b7?w=800&h=400&fit=crop&crop=center",
      category: "Ecosystems",
      type: "Marine Biology",
      author: "Dr. Coral Martinez",
      authorAvatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=center",
      publishedAt: "4 days ago",
      readTime: "11 min read",
      featured: false,
      facts: [
        "Support 25% of marine species",
        "Cover less than 1% of ocean floor",
        "Provide food for 1 billion people"
      ],
      impact: "Essential for marine biodiversity and coastal protection"
    }
  ];

  const filteredContent = planetContent.filter(content => {
    const matchesCategory = selectedCategory === 'All' || content.category === selectedCategory;
    const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.type.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredContent = filteredContent.find(content => content.featured);
  const regularContent = filteredContent.filter(content => !content.featured);

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Climate': return '#ef4444';
      case 'Ecosystems': return '#10b981';
      case 'Geology': return '#f59e0b';
      case 'Oceans': return '#3b82f6';
      case 'Atmosphere': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  return (
    <div className="planet-page">
      {/* Hero Section */}
      <section className="planet-hero">
        <div className="planet-hero-container">
          <div className="planet-hero-content">
            <h1 className="planet-hero-title">
              Our <span className="planet-hero-highlight">Planet</span>
            </h1>
            <p className="planet-hero-subtitle">
              Explore the wonders of Earth - from the deepest oceans to the highest atmosphere. 
              Discover the intricate systems that make our planet unique in the universe.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="planet-content">
        <div className="planet-container">
          {/* Search and Filter Controls */}
          <div className="planet-controls">
            <div className="planet-search-container">
              <input
                type="text"
                placeholder="Search Earth sciences..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="planet-search-input"
              />
              <svg className="planet-search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>

            <div className="planet-category-filters">
              {planetCategories.map(category => (
                <button
                  key={category}
                  className={`planet-category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Content */}
          {featuredContent && (
            <div className="planet-featured">
              <div className="planet-featured-content">
                <div className="planet-featured-image">
                  <img src={featuredContent.image} alt={featuredContent.title} />
                  <div className="planet-featured-badge">Featured Study</div>
                  <div className="planet-category-badge" style={{ backgroundColor: getCategoryColor(featuredContent.category) }}>
                    {featuredContent.category}
                  </div>
                </div>
                <div className="planet-featured-info">
                  <div className="planet-featured-meta">
                    <span className="planet-type-tag">{featuredContent.type}</span>
                    <span className="planet-read-time">{featuredContent.readTime}</span>
                  </div>
                  <h2 className="planet-featured-title">{featuredContent.title}</h2>
                  <p className="planet-featured-description">{featuredContent.description}</p>
                  
                  <div className="planet-featured-author">
                    <img src={featuredContent.authorAvatar} alt={featuredContent.author} className="planet-author-avatar" />
                    <div className="planet-author-info">
                      <span className="planet-author-name">{featuredContent.author}</span>
                      <span className="planet-publish-date">{featuredContent.publishedAt}</span>
                    </div>
                  </div>

                  <div className="planet-facts">
                    <h4>Key Facts</h4>
                    <ul className="planet-facts-list">
                      {featuredContent.facts.map((fact, index) => (
                        <li key={index} className="planet-fact-item">{fact}</li>
                      ))}
                    </ul>
                  </div>

                  <div className="planet-impact">
                    <h4>Global Impact</h4>
                    <p className="planet-impact-text">{featuredContent.impact}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Content Grid */}
          <div className="planet-grid">
            {regularContent.map(content => (
              <article key={content.id} className="planet-card">
                <div className="planet-card-image">
                  <img src={content.image} alt={content.title} />
                  <div className="planet-card-category" style={{ backgroundColor: getCategoryColor(content.category) }}>
                    {content.category}
                  </div>
                  <div className="planet-card-type">{content.type}</div>
                </div>
                <div className="planet-card-content">
                  <div className="planet-card-meta">
                    <span className="planet-card-read-time">{content.readTime}</span>
                    <span className="planet-card-date">{content.publishedAt}</span>
                  </div>
                  <h3 className="planet-card-title">{content.title}</h3>
                  <p className="planet-card-description">{content.description}</p>
                  
                  <div className="planet-card-author">
                    <img src={content.authorAvatar} alt={content.author} className="planet-card-author-avatar" />
                    <span className="planet-card-author-name">{content.author}</span>
                  </div>

                  <div className="planet-card-facts">
                    <h5>Quick Facts</h5>
                    <div className="planet-card-facts-preview">
                      {content.facts.slice(0, 2).map((fact, index) => (
                        <div key={index} className="planet-fact-preview">{fact}</div>
                      ))}
                    </div>
                  </div>

                  <div className="planet-card-impact">
                    <span className="planet-impact-label">Impact:</span>
                    <span className="planet-impact-preview">{content.impact}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {filteredContent.length === 0 && (
            <div className="planet-no-results">
              <p>No content found matching your search criteria.</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default OurPlanetPage;

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './Footer.css';

const Footer = () => {
  const [email, setEmail] = useState('');

  const handleNewsletterSubmit = (e) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email);
    setEmail('');
  };

  const footerSections = [
    {
      title: "Space and Universe",
      links: [
        "NASA Missions",
        "Extraterrestrials",
        "New Planet Discovery",
        "Neighborhood Planets",
        "Satellite News",
        "Astronaut Insights"
      ]
    },
    {
      title: "Our Planet",
      links: [
        "Earth",
        "Discover Animals",
        "Plants and the Life",
        "Human & Civilization",
        "History & Future",
        "Predict the Future"
      ]
    },
    {
      title: "Health and Science",
      links: [
        "Newest Science",
        "Science Projects",
        "Science for Humanity",
        "The Reality of Science",
        "Human Health",
        "Animals & Plants Health"
      ]
    },
    {
      title: "Technology",
      links: [
        "Newest Technology",
        "Technology for Humanity",
        "Technology for Animals",
        "Technology for Plants",
        "Technology for the Planet",
        "Technology for the Future"
      ]
    },
    {
      title: "Our Community",
      links: [
        "About Us",
        "Advertise",
        "Events",
        "People Insights",
        "Satellite News",
        "Astronaut Insights"
      ]
    },
    {
      title: "Our Podcasts",
      links: [
        "It's All About Earth!",
        "Become an Astronauts",
        "Discover Other Life?",
        "Our Neighborhoods",
        "Earth From Space",
        "The Future of Human"
      ]
    }
  ];

  const socialLinks = [
    {
      name: 'Instagram',
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="social-svg">
          <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
        </svg>
      ),
      url: '#'
    },
    {
      name: 'Twitter',
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="social-svg">
          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
        </svg>
      ),
      url: '#'
    },
    {
      name: 'Facebook',
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="social-svg">
          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </svg>
      ),
      url: '#'
    },
    {
      name: 'Threads',
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="social-svg">
          <path d="M12.186 24h-.007c-3.581-.024-6.334-1.205-8.184-3.509C2.35 18.44 1.5 15.586 1.472 12.01v-.017c.03-3.579.879-6.43 2.525-8.482C5.845 1.205 8.6.024 12.18 0h.014c2.746.02 5.043.725 6.826 2.098 1.677 1.29 2.858 3.13 3.509 5.467l-2.04.569c-1.104-3.96-3.898-5.984-8.304-6.015-2.91.022-5.11.936-6.54 2.717C4.307 6.504 3.616 8.914 3.589 12c.027 3.086.718 5.496 2.057 7.164 1.43 1.781 3.631 2.695 6.54 2.717 2.623-.02 4.358-.631 5.8-2.045 1.647-1.613 1.618-3.593 1.09-4.798-.31-.71-.873-1.3-1.634-1.75-.192 1.352-.622 2.446-1.284 3.272-.886 1.102-2.14 1.704-3.73 1.79-1.202.065-2.361-.218-3.259-.801-1.063-.689-1.685-1.74-1.752-2.964-.065-1.19.408-2.285 1.33-3.082.88-.76 2.119-1.207 3.583-1.291a13.853 13.853 0 013.02.142c-.126-.742-.375-1.332-.74-1.811-.365-.479-.856-.735-1.474-.77-1.014-.058-1.86.27-2.538.984l-1.43-1.43c.96-.96 2.31-1.467 4.01-1.384 1.18.056 2.227.56 3.022 1.455.795.895 1.225 2.074 1.225 3.4v.909c1.165.414 2.172 1.084 2.922 1.895 1.063 1.151 1.602 2.618 1.602 4.36 0 1.748-.648 3.368-1.816 4.536C18.516 22.916 15.648 23.98 12.186 24z"/>
        </svg>
      ),
      url: '#'
    },
    {
      name: 'GitHub',
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="social-svg">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
      ),
      url: '#'
    }
  ];

  return (
    <footer className="footer">
      <div className="footer-container">
        {/* Header Section */}
        <div className="footer-header">
          <div className="footer-brand">
            <h2 className="footer-logo">MB Space</h2>
            <div className="footer-social">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  className="social-link"
                  aria-label={social.name}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          <div className="footer-newsletter">
            <h3 className="newsletter-title">Subscribe to our newsletter</h3>
            <form onSubmit={handleNewsletterSubmit} className="newsletter-form">
              <div className="newsletter-input-container">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email here..."
                  className="newsletter-input"
                  required
                />
                <button type="submit" className="newsletter-submit">
                  <svg viewBox="0 0 24 24" fill="currentColor" className="submit-icon">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Links Section */}
        <div className="footer-links">
          {footerSections.map((section, index) => (
            <div key={index} className="footer-column">
              <h4 className="footer-column-title">{section.title}</h4>
              <ul className="footer-column-links">
                {section.links.map((link, linkIndex) => {
                  // Map specific links to routes
                  const linkMap = {
                    'NASA Missions': '/news',
                    'Newest Technology': '/technology',
                    'Earth': '/our-planet',
                    'Newest Science': '/health-science',
                    'About Us': '/our-planet',
                    "It's All About Earth!": '/our-planet'
                  };

                  const route = linkMap[link];

                  return (
                    <li key={linkIndex}>
                      {route ? (
                        <Link to={route} className="footer-link">{link}</Link>
                      ) : (
                        <a href="#" className="footer-link">{link}</a>
                      )}
                    </li>
                  );
                })}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="footer-bottom">
          <div className="footer-copyright">
            <p>© 2023 MB Space, LLC. All Rights Reserved. Use of this site constitutes acceptance of our
              <a href="#" className="legal-link"> Terms of Service</a>,
              <a href="#" className="legal-link"> Privacy Policy</a> and
              <a href="#" className="legal-link"> Do Not Sell or Share My Personal Information</a>.
              MB Space may receive compensation for some links to products and services on this website.
              Offers may be subject to change without notice.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

/* Health & Science Page - Space Theme */
.health-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding-top: 70px;
}

/* Hero Section */
.health-hero {
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.health-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.health-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

.health-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.health-hero-highlight {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.health-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #b3b3b3;
  max-width: 650px;
  margin: 0 auto;
}

/* Content Section */
.health-content {
  padding: 2rem 0 4rem;
  position: relative;
}

.health-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.05), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.03), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.health-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Controls */
.health-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.health-search-container {
  position: relative;
  max-width: 450px;
  margin: 0 auto;
}

.health-search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem 0.75rem 3rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.health-search-input:focus {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.health-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.health-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
}

.health-category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.health-category-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.health-category-btn:hover,
.health-category-btn.active {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
}

/* Featured Research */
.health-featured {
  margin-bottom: 3rem;
}

.health-featured-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  transition: all 0.3s ease;
}

.health-featured-content:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.health-featured-image {
  position: relative;
  height: 450px;
  overflow: hidden;
}

.health-featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.health-featured-content:hover .health-featured-image img {
  transform: scale(1.05);
}

.health-featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.health-category-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.health-featured-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.health-featured-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.health-study-type {
  background: rgba(79, 70, 229, 0.2);
  color: #a5b4fc;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.health-institution {
  color: #b3b3b3;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.health-featured-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  line-height: 1.3;
}

.health-featured-description {
  font-size: 1rem;
  color: #d1d5db;
  line-height: 1.6;
}

.health-study-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.health-detail-item {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem;
  border-radius: 8px;
}

.health-detail-label {
  font-size: 0.8rem;
  color: #b3b3b3;
  margin-bottom: 0.25rem;
}

.health-detail-value {
  font-size: 0.9rem;
  color: white;
  font-weight: 600;
}

.health-featured-author {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.health-author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.health-author-name {
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
}

.health-publish-date {
  color: #b3b3b3;
  font-size: 0.8rem;
}

.health-findings h4,
.health-implications h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #a5b4fc;
}

.health-findings-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.health-finding-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d1d5db;
  font-size: 0.9rem;
}

.health-finding-item::before {
  content: '🔬';
  font-size: 0.8rem;
}

.health-implications-text {
  color: #d1d5db;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
  font-style: italic;
  background: rgba(79, 70, 229, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
  border-left: 3px solid #4f46e5;
}

/* Research Grid */
.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.health-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.health-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.health-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.health-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.health-card:hover .health-card-image img {
  transform: scale(1.05);
}

.health-card-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.health-card-type {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.health-card-content {
  padding: 1.5rem;
}

.health-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #b3b3b3;
}

.health-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: white;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.health-card-description {
  font-size: 0.9rem;
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.health-card-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  gap: 1rem;
}

.health-card-detail {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.health-card-detail-label {
  font-size: 0.75rem;
  color: #b3b3b3;
  margin-bottom: 0.25rem;
}

.health-card-detail-value {
  font-size: 0.8rem;
  color: white;
  font-weight: 500;
}

.health-card-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.health-card-author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.health-card-author-name {
  font-size: 0.85rem;
  color: #d1d5db;
  font-weight: 500;
}

.health-card-findings h5 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #a5b4fc;
}

.health-card-findings-preview {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.health-finding-preview {
  font-size: 0.8rem;
  color: #d1d5db;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.health-finding-preview::before {
  content: '•';
  color: #10b981;
  font-weight: bold;
}

.health-card-implications {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.health-implications-label {
  font-size: 0.8rem;
  color: #a5b4fc;
  font-weight: 600;
}

.health-implications-preview {
  font-size: 0.8rem;
  color: #d1d5db;
  line-height: 1.4;
  font-style: italic;
}

/* No Results */
.health-no-results {
  text-align: center;
  padding: 3rem;
  color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .health-hero-title {
    font-size: 2.5rem;
  }

  .health-hero-subtitle {
    font-size: 1.1rem;
  }

  .health-container {
    padding: 0 1rem;
  }

  .health-featured-content {
    grid-template-columns: 1fr;
  }

  .health-featured-image {
    height: 250px;
  }

  .health-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .health-category-filters {
    gap: 0.5rem;
  }

  .health-category-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .health-study-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .health-hero {
    padding: 2rem 0;
  }

  .health-hero-title {
    font-size: 2rem;
  }

  .health-featured-info {
    padding: 1.5rem;
  }

  .health-card-content {
    padding: 1rem;
  }

  .health-search-container {
    max-width: 100%;
  }

  .health-card-details {
    flex-direction: column;
    gap: 0.5rem;
  }
}

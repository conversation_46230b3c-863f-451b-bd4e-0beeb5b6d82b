// Podcast API Service using Listen Notes API
// Documentation: https://www.listennotes.com/api/docs/

const LISTEN_NOTES_API_BASE = 'https://listen-api.listennotes.com/api/v2';
const MOCK_API_BASE = 'https://listen-api-test.listennotes.com/api/v2'; // For testing without API key

// Use mock API for development, real API for production
const API_BASE = process.env.NODE_ENV === 'production' ? LISTEN_NOTES_API_BASE : MOCK_API_BASE;
const API_KEY = process.env.REACT_APP_LISTEN_NOTES_API_KEY || null;

/**
 * Makes a request to the Listen Notes API
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} API response
 */
async function makeApiRequest(endpoint, params = {}) {
  try {
    const url = new URL(`${API_BASE}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    const headers = {
      'Accept': 'application/json',
    };

    // Add API key for production
    if (API_KEY && process.env.NODE_ENV === 'production') {
      headers['X-ListenAPI-Key'] = API_KEY;
    }

    console.log('Making API request to:', url.toString());

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

/**
 * Searches for podcast episodes
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<Array>} Array of episodes
 */
export async function searchEpisodes(query = 'space technology', options = {}) {
  const params = {
    q: query,
    type: 'episode',
    len_min: 10, // Minimum episode length in minutes
    len_max: 120, // Maximum episode length in minutes
    published_after: Math.floor(Date.now() / 1000) - (365 * 24 * 60 * 60), // Last year
    sort_by_date: 0, // Sort by relevance
    safe_mode: 1, // Family safe content
    ...options
  };

  try {
    const data = await makeApiRequest('/search', params);
    return data.results || [];
  } catch (error) {
    console.error('Error searching episodes:', error);
    throw error;
  }
}

/**
 * Gets best podcasts by category
 * @param {string} category - Category name
 * @param {Object} options - Options
 * @returns {Promise<Array>} Array of podcasts
 */
export async function getBestPodcasts(category = 'Technology', options = {}) {
  const params = {
    genre_id: getCategoryId(category),
    page: 1,
    region: 'us',
    safe_mode: 1,
    ...options
  };

  try {
    const data = await makeApiRequest('/best_podcasts', params);
    return data.podcasts || [];
  } catch (error) {
    console.error('Error getting best podcasts:', error);
    throw error;
  }
}

/**
 * Gets episodes from a specific podcast
 * @param {string} podcastId - Podcast ID
 * @param {Object} options - Options
 * @returns {Promise<Array>} Array of episodes
 */
export async function getPodcastEpisodes(podcastId, options = {}) {
  const params = {
    next_episode_pub_date: Math.floor(Date.now() / 1000),
    sort: 'recent_first',
    ...options
  };

  try {
    const data = await makeApiRequest(`/podcasts/${podcastId}`, params);
    return data.episodes || [];
  } catch (error) {
    console.error('Error getting podcast episodes:', error);
    throw error;
  }
}

/**
 * Gets episode details by ID
 * @param {string} episodeId - Episode ID
 * @returns {Promise<Object>} Episode details
 */
export async function getEpisodeById(episodeId) {
  try {
    const data = await makeApiRequest(`/episodes/${episodeId}`);
    return data;
  } catch (error) {
    console.error('Error getting episode details:', error);
    throw error;
  }
}

/**
 * Maps category names to Listen Notes genre IDs
 * @param {string} category - Category name
 * @returns {number} Genre ID
 */
function getCategoryId(category) {
  const categoryMap = {
    'Technology': 127,
    'Science': 107,
    'Exploration': 127, // Using Technology as fallback
    'News': 99,
    'Lifestyle': 88,
    'Travel': 122,
    'Environment': 107, // Using Science as fallback
    'Education': 111,
    'Business': 93,
    'Health': 88
  };
  
  return categoryMap[category] || 127; // Default to Technology
}

/**
 * Formats duration from seconds to MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
export function formatDuration(seconds) {
  if (!seconds || isNaN(seconds)) return '0:00';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Formats publish date to relative time
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Relative time string
 */
export function formatPublishDate(timestamp) {
  if (!timestamp) return 'Unknown';
  
  const now = Date.now();
  const publishDate = timestamp * 1000; // Convert to milliseconds
  const diffMs = now - publishDate;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

/**
 * Transforms Listen Notes episode data to our component format
 * @param {Object} episode - Episode data from API
 * @param {number} index - Episode index for ID generation
 * @returns {Object} Transformed episode data
 */
export function transformEpisodeData(episode, index = 0) {
  return {
    id: episode.id || `episode-${index}`,
    title: episode.title_original || episode.title || 'Untitled Episode',
    description: episode.description_original || episode.description || 'No description available.',
    thumbnail: episode.thumbnail || episode.image || 'https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center',
    duration: formatDuration(episode.audio_length_sec),
    publishedAt: formatPublishDate(episode.pub_date_ms ? Math.floor(episode.pub_date_ms / 1000) : null),
    audioUrl: episode.audio || episode.link || null,
    host: {
      name: episode.podcast?.publisher_original || episode.podcast?.publisher || 'Unknown Host',
      avatar: episode.podcast?.thumbnail || episode.podcast?.image || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center'
    },
    category: episode.podcast?.genre_ids?.[0] ? getCategoryNameById(episode.podcast.genre_ids[0]) : 'Technology',
    featured: index === 0, // Mark first episode as featured
    listens: episode.listennotes_url ? `${Math.floor(Math.random() * 20) + 1}.${Math.floor(Math.random() * 9)}K` : '0'
  };
}

/**
 * Maps genre IDs back to category names
 * @param {number} genreId - Genre ID
 * @returns {string} Category name
 */
function getCategoryNameById(genreId) {
  const genreMap = {
    127: 'Technology',
    107: 'Science',
    99: 'News',
    88: 'Lifestyle',
    122: 'Travel',
    111: 'Education',
    93: 'Business'
  };
  
  return genreMap[genreId] || 'Technology';
}

/**
 * Gets curated space-related podcast episodes
 * @returns {Promise<Array>} Array of space-related episodes
 */
export async function getSpaceRelatedEpisodes() {
  const spaceQueries = [
    'space exploration',
    'NASA missions',
    'astronomy discoveries',
    'SpaceX launches',
    'Mars exploration',
    'space technology',
    'astrophysics',
    'space science'
  ];

  try {
    // Get episodes from multiple space-related searches
    const searchPromises = spaceQueries.slice(0, 3).map(query => 
      searchEpisodes(query, { len_min: 15, len_max: 90 })
    );

    const searchResults = await Promise.all(searchPromises);
    
    // Flatten and deduplicate results
    const allEpisodes = searchResults.flat();
    const uniqueEpisodes = allEpisodes.filter((episode, index, self) => 
      index === self.findIndex(e => e.id === episode.id)
    );

    // Transform and return top episodes
    return uniqueEpisodes
      .slice(0, 12)
      .map((episode, index) => transformEpisodeData(episode, index));
      
  } catch (error) {
    console.error('Error getting space-related episodes:', error);
    // Return fallback data if API fails
    return getFallbackEpisodes();
  }
}

/**
 * Fallback episode data when API is unavailable
 * @returns {Array} Fallback episodes
 */
function getFallbackEpisodes() {
  return [
    {
      id: 'fallback-1',
      title: "Today's Technology with Danny Reighman",
      description: "On this podcast, I talk about what happened in technology. Exploring the latest developments in space technology and their impact on future missions.",
      thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
      duration: "45:32",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Danny Reighman",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
      },
      category: "Technology",
      featured: true,
      listens: "12.5K"
    }
    // Add more fallback episodes as needed
  ];
}

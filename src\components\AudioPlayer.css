/* Audio Player Styles */
.audio-player {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.audio-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.error-message {
  color: #fca5a5;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message::before {
  content: '⚠';
  font-size: 1rem;
}

/* Player Controls */
.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.skip-btn,
.play-pause-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.skip-btn {
  width: 40px;
  height: 40px;
}

.play-pause-btn {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-color: rgba(79, 70, 229, 0.5);
}

.skip-btn:hover,
.play-pause-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
}

.skip-btn:disabled,
.play-pause-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.skip-btn svg,
.play-pause-btn svg {
  width: 20px;
  height: 20px;
}

.play-pause-btn svg {
  width: 24px;
  height: 24px;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress Section */
.progress-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.time-display {
  font-size: 0.8rem;
  color: #b3b3b3;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.progress-container {
  flex: 1;
}

.progress-bar {
  position: relative;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-bar:hover .progress-thumb {
  opacity: 1;
}

.progress-bar:focus {
  outline: none;
}

.progress-bar:focus .progress-track {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5);
}

/* Volume Section */
.volume-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.volume-icon {
  width: 20px;
  height: 20px;
  color: #b3b3b3;
  flex-shrink: 0;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.volume-slider::-moz-range-track {
  background: rgba(255, 255, 255, 0.2);
  height: 4px;
  border-radius: 2px;
  border: none;
}

/* Featured Episode Audio Player */
.featured-episode .audio-player {
  margin-top: 1rem;
  background: rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .audio-player {
    padding: 0.75rem;
  }

  .player-controls {
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .skip-btn {
    width: 36px;
    height: 36px;
  }

  .play-pause-btn {
    width: 44px;
    height: 44px;
  }

  .skip-btn svg,
  .play-pause-btn svg {
    width: 18px;
    height: 18px;
  }

  .play-pause-btn svg {
    width: 20px;
    height: 20px;
  }

  .progress-section {
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .time-display {
    font-size: 0.75rem;
    min-width: 35px;
  }

  .volume-section {
    gap: 0.5rem;
  }

  .volume-icon {
    width: 18px;
    height: 18px;
  }

  .volume-slider {
    width: 60px;
  }
}

@media (max-width: 480px) {
  .audio-player {
    padding: 0.5rem;
  }

  .player-controls {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .skip-btn {
    width: 32px;
    height: 32px;
  }

  .play-pause-btn {
    width: 40px;
    height: 40px;
  }

  .skip-btn svg {
    width: 16px;
    height: 16px;
  }

  .play-pause-btn svg {
    width: 18px;
    height: 18px;
  }

  .progress-section {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .time-display {
    font-size: 0.7rem;
    min-width: 30px;
  }

  .volume-section {
    justify-content: center;
  }

  .volume-slider {
    width: 50px;
  }

  .progress-thumb {
    width: 10px;
    height: 10px;
  }

  .volume-slider::-webkit-slider-thumb {
    width: 10px;
    height: 10px;
  }

  .volume-slider::-moz-range-thumb {
    width: 10px;
    height: 10px;
  }
}

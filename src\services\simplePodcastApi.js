// Simple Podcast API Service
// This is a working implementation that can be enhanced with real APIs later

const API_BASE_URL = 'https://listen-api.listennotes.com/api/v2';
const API_KEY = process.env.REACT_APP_LISTEN_NOTES_API_KEY;

// Mock data for development
const mockPodcastData = [
  {
    id: 1,
    title: "Space Technology Today",
    description: "Exploring the latest developments in space technology and their impact on future missions. Join us as we dive deep into rocket propulsion, satellite technology, and the future of space exploration.",
    thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
    duration: "45:32",
    publishedAt: "2 days ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Dr. <PERSON>",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
    },
    category: "Technology",
    listens: "12.5K",
    featured: true
  },
  {
    id: 2,
    title: "Mars Mission Updates",
    description: "Weekly updates on Mars missions, discoveries, and the latest developments in space exploration. Get the inside scoop on NASA's latest findings.",
    thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop&crop=center",
    duration: "38:15",
    publishedAt: "4 days ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Dr. Mike Chen",
      avatar: "https://images.unsplash.com/photo-*************-2616b612b786?w=40&h=40&fit=crop&crop=center"
    },
    category: "Science",
    listens: "8.7K"
  },
  {
    id: 3,
    title: "Astronaut Life Stories",
    description: "Personal stories and experiences from astronauts who have lived and worked in space. Hear firsthand accounts of life aboard the International Space Station.",
    thumbnail: "https://images.unsplash.com/photo-*************-2c8b550f87b3?w=400&h=400&fit=crop&crop=center",
    duration: "52:18",
    publishedAt: "1 week ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Commander Lisa Park",
      avatar: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=40&h=40&fit=crop&crop=center"
    },
    category: "Exploration",
    listens: "15.2K"
  },
  {
    id: 4,
    title: "Future of Space Travel",
    description: "Discussing the future of commercial space travel, space tourism, and what it means for humanity's expansion into the cosmos.",
    thumbnail: "https://images.unsplash.com/photo-*************-5750f3195933?w=400&h=400&fit=crop&crop=center",
    duration: "41:07",
    publishedAt: "1 week ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Dr. Alex Rivera",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=center"
    },
    category: "Technology",
    listens: "9.3K"
  },
  {
    id: 5,
    title: "Space News Weekly",
    description: "Your weekly roundup of space news, including launches, discoveries, and industry updates from around the world.",
    thumbnail: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=400&h=400&fit=crop&crop=center",
    duration: "29:45",
    publishedAt: "3 days ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Emma Thompson",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=center"
    },
    category: "News",
    listens: "11.8K"
  },
  {
    id: 6,
    title: "Deep Space Discoveries",
    description: "Exploring the mysteries of deep space, black holes, exoplanets, and the search for extraterrestrial life.",
    thumbnail: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=400&fit=crop&crop=center",
    duration: "47:22",
    publishedAt: "5 days ago",
    audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
    host: {
      name: "Dr. James Wilson",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
    },
    category: "Science",
    listens: "13.7K"
  }
];

/**
 * Simulate API delay for realistic experience
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Get space-related podcast episodes
 * @returns {Promise<Array>} Array of podcast episodes
 */
export const getSpaceRelatedEpisodes = async () => {
  try {
    // Simulate API call delay
    await delay(800);
    
    // In development, return mock data
    if (!API_KEY || process.env.NODE_ENV === 'development') {
      console.log('Using mock podcast data');
      return mockPodcastData;
    }

    // Real API call would go here
    const response = await fetch(`${API_BASE_URL}/search?q=space%20technology&type=episode&len_min=10&len_max=120&sort_by_date=1&only_in=title,description`, {
      headers: {
        'X-ListenAPI-Key': API_KEY
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch podcast data');
    }

    const data = await response.json();
    return data.results.map(transformEpisodeData);
  } catch (error) {
    console.error('Error fetching podcast episodes:', error);
    // Fallback to mock data on error
    return mockPodcastData;
  }
};

/**
 * Search for podcast episodes
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<Array>} Array of podcast episodes
 */
export const searchEpisodes = async (query, options = {}) => {
  try {
    await delay(500);
    
    // Filter mock data based on query
    const filtered = mockPodcastData.filter(episode => 
      episode.title.toLowerCase().includes(query.toLowerCase()) ||
      episode.description.toLowerCase().includes(query.toLowerCase()) ||
      episode.host.name.toLowerCase().includes(query.toLowerCase())
    );
    
    return filtered;
  } catch (error) {
    console.error('Error searching episodes:', error);
    return [];
  }
};

/**
 * Transform API episode data to our format
 * @param {Object} episode - Raw episode data from API
 * @param {number} index - Episode index for ID generation
 * @returns {Object} Transformed episode data
 */
export const transformEpisodeData = (episode, index = 0) => {
  return {
    id: episode.id || index + 1,
    title: episode.title_original || episode.title || 'Untitled Episode',
    description: episode.description_original || episode.description || 'No description available',
    thumbnail: episode.thumbnail || episode.image || 'https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center',
    duration: formatDuration(episode.audio_length_sec || 2700),
    publishedAt: formatPublishDate(episode.pub_date_ms),
    audioUrl: episode.audio || 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    host: {
      name: episode.podcast?.publisher || 'Unknown Host',
      avatar: episode.podcast?.thumbnail || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center'
    },
    category: determineCategory(episode.title_original || episode.title || ''),
    listens: generateListenCount(),
    featured: index === 0
  };
};

/**
 * Format duration from seconds to MM:SS
 */
const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Format publish date
 */
const formatPublishDate = (timestamp) => {
  if (!timestamp) return 'Recently';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`;
  return `${Math.ceil(diffDays / 30)} month${Math.ceil(diffDays / 30) > 1 ? 's' : ''} ago`;
};

/**
 * Determine category based on title/content
 */
const determineCategory = (title) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes('technology') || titleLower.includes('tech')) return 'Technology';
  if (titleLower.includes('science') || titleLower.includes('research')) return 'Science';
  if (titleLower.includes('exploration') || titleLower.includes('mission')) return 'Exploration';
  if (titleLower.includes('news') || titleLower.includes('update')) return 'News';
  return 'Science';
};

/**
 * Generate random listen count
 */
const generateListenCount = () => {
  const count = Math.floor(Math.random() * 20000) + 1000;
  if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K';
  }
  return count.toString();
};

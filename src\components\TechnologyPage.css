/* Technology Page - Space Theme */
.technology-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding-top: 70px;
}

/* Hero Section */
.tech-hero {
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.tech-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.tech-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  text-align: center;
}

.tech-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tech-hero-highlight {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tech-hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #b3b3b3;
  max-width: 600px;
  margin: 0 auto;
}

/* Content Section */
.tech-content {
  padding: 2rem 0 4rem;
  position: relative;
}

.tech-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.05), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.03), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.tech-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Controls */
.tech-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.tech-search-container {
  position: relative;
  max-width: 400px;
  margin: 0 auto;
}

.tech-search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem 0.75rem 3rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.tech-search-input:focus {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.tech-search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.tech-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.5);
}

.tech-category-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.tech-category-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #d1d5db;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.tech-category-btn:hover,
.tech-category-btn.active {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
}

/* Featured Technology */
.tech-featured {
  margin-bottom: 3rem;
}

.tech-featured-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  transition: all 0.3s ease;
}

.tech-featured-content:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.tech-featured-image {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.tech-featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tech-featured-content:hover .tech-featured-image img {
  transform: scale(1.05);
}

.tech-featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tech-status-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tech-featured-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.tech-featured-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.tech-category-tag {
  background: rgba(79, 70, 229, 0.2);
  color: #a5b4fc;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.tech-organization,
.tech-timeline {
  color: #b3b3b3;
  font-size: 0.8rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.tech-featured-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  line-height: 1.3;
}

.tech-featured-description {
  font-size: 1rem;
  color: #d1d5db;
  line-height: 1.6;
}

.tech-specs h4,
.tech-benefits h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: #a5b4fc;
}

.tech-specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.tech-spec-item {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem;
  border-radius: 8px;
}

.tech-spec-label {
  font-size: 0.8rem;
  color: #b3b3b3;
  margin-bottom: 0.25rem;
}

.tech-spec-value {
  font-size: 0.9rem;
  color: white;
  font-weight: 600;
}

.tech-benefits-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tech-benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d1d5db;
  font-size: 0.9rem;
}

.tech-benefit-item::before {
  content: '✓';
  color: #10b981;
  font-weight: bold;
}

/* Technologies Grid */
.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.tech-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.tech-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.tech-card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.tech-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.tech-card:hover .tech-card-image img {
  transform: scale(1.05);
}

.tech-card-category {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(79, 70, 229, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tech-card-status {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tech-card-content {
  padding: 1.5rem;
}

.tech-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: #b3b3b3;
}

.tech-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: white;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tech-card-description {
  font-size: 0.9rem;
  color: #d1d5db;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tech-card-specs h5 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #a5b4fc;
}

.tech-card-specs-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.tech-card-spec {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
}

.tech-card-spec-label {
  color: #b3b3b3;
}

.tech-card-spec-value {
  color: white;
  font-weight: 500;
}

.tech-card-benefits-preview {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tech-benefit-tag {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
  padding: 0.2rem 0.5rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* No Results */
.tech-no-results {
  text-align: center;
  padding: 3rem;
  color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tech-hero-title {
    font-size: 2.5rem;
  }

  .tech-hero-subtitle {
    font-size: 1.1rem;
  }

  .tech-container {
    padding: 0 1rem;
  }

  .tech-featured-content {
    grid-template-columns: 1fr;
  }

  .tech-featured-image {
    height: 250px;
  }

  .tech-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tech-category-filters {
    gap: 0.5rem;
  }

  .tech-category-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .tech-hero {
    padding: 2rem 0;
  }

  .tech-hero-title {
    font-size: 2rem;
  }

  .tech-featured-info {
    padding: 1.5rem;
  }

  .tech-card-content {
    padding: 1rem;
  }

  .tech-search-container {
    max-width: 100%;
  }

  .tech-specs-grid {
    grid-template-columns: 1fr;
  }
}

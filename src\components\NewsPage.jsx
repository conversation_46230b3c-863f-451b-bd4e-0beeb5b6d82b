import React, { useState } from 'react';
import './NewsPage.css';

const NewsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const newsCategories = ['All', 'Breaking', 'Missions', 'Discoveries', 'Technology', 'Research'];

  const newsArticles = [
    {
      id: 1,
      title: "NASA's Artemis III Mission Targets Moon's South Pole",
      excerpt: "The upcoming Artemis III mission aims to land the first woman and next man on the lunar surface, focusing on the resource-rich south polar region.",
      content: "NASA's ambitious Artemis III mission represents a historic milestone in space exploration...",
      image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=800&h=400&fit=crop&crop=center",
      category: "Missions",
      author: "Dr. <PERSON>",
      authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center",
      publishedAt: "2 hours ago",
      readTime: "5 min read",
      featured: true,
      tags: ["NASA", "Moon", "Artemis", "Space Exploration"]
    },
    {
      id: 2,
      title: "<PERSON> Telescope Discovers Oldest Galaxy Yet",
      excerpt: "Astronomers using the James Webb Space Telescope have identified a galaxy that formed just 400 million years after the Big Bang.",
      content: "This groundbreaking discovery pushes back the frontier of cosmic observation...",
      image: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=800&h=400&fit=crop&crop=center",
      category: "Discoveries",
      author: "Prof. Michael Chen",
      authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center",
      publishedAt: "6 hours ago",
      readTime: "7 min read",
      featured: false,
      tags: ["JWST", "Galaxy", "Astronomy", "Deep Space"]
    },
    {
      id: 3,
      title: "SpaceX Starship Completes Successful Orbital Test",
      excerpt: "The latest Starship prototype has achieved a major milestone with its first successful orbital flight and landing sequence.",
      content: "SpaceX's Starship program reaches new heights with this successful test...",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800&h=400&fit=crop&crop=center",
      category: "Technology",
      author: "Emma Rodriguez",
      authorAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=center",
      publishedAt: "12 hours ago",
      readTime: "4 min read",
      featured: false,
      tags: ["SpaceX", "Starship", "Rocket", "Mars"]
    },
    {
      id: 4,
      title: "International Space Station Welcomes New Crew",
      excerpt: "Seven astronauts from five different countries have arrived at the ISS for a six-month research mission.",
      content: "The diverse crew will conduct over 200 scientific experiments...",
      image: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=800&h=400&fit=crop&crop=center",
      category: "Missions",
      author: "Commander Lisa Park",
      authorAvatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=40&h=40&fit=crop&crop=center",
      publishedAt: "1 day ago",
      readTime: "6 min read",
      featured: false,
      tags: ["ISS", "Astronauts", "Research", "International"]
    },
    {
      id: 5,
      title: "Mars Rover Perseverance Finds Evidence of Ancient Water",
      excerpt: "New analysis of rock samples collected by Perseverance reveals strong evidence of ancient river systems on Mars.",
      content: "The discovery provides crucial insights into Mars' watery past...",
      image: "https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?w=800&h=400&fit=crop&crop=center",
      category: "Discoveries",
      author: "Dr. Alex Turner",
      authorAvatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=40&h=40&fit=crop&crop=center",
      publishedAt: "2 days ago",
      readTime: "8 min read",
      featured: false,
      tags: ["Mars", "Perseverance", "Water", "Geology"]
    },
    {
      id: 6,
      title: "Breakthrough in Fusion Propulsion Technology",
      excerpt: "Scientists achieve a major breakthrough in fusion-powered spacecraft propulsion that could revolutionize deep space travel.",
      content: "This advancement could reduce travel time to Mars by 75%...",
      image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=400&fit=crop&crop=center",
      category: "Technology",
      author: "Dr. Robert Kim",
      authorAvatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=40&h=40&fit=crop&crop=center",
      publishedAt: "3 days ago",
      readTime: "10 min read",
      featured: false,
      tags: ["Fusion", "Propulsion", "Deep Space", "Innovation"]
    }
  ];

  const filteredArticles = newsArticles.filter(article => {
    const matchesCategory = selectedCategory === 'All' || article.category === selectedCategory;
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const featuredArticle = filteredArticles.find(article => article.featured);
  const regularArticles = filteredArticles.filter(article => !article.featured);

  return (
    <div className="news-page">
      {/* Hero Section */}
      <section className="news-hero">
        <div className="news-hero-container">
          <div className="news-hero-content">
            <h1 className="news-hero-title">
              Space <span className="news-hero-highlight">News</span>
            </h1>
            <p className="news-hero-subtitle">
              Stay updated with the latest developments in space exploration, 
              scientific discoveries, and technological breakthroughs from around the universe.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="news-content">
        <div className="news-container">
          {/* Search and Filter Controls */}
          <div className="news-controls">
            <div className="news-search-container">
              <input
                type="text"
                placeholder="Search news articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="news-search-input"
              />
              <svg className="news-search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>

            <div className="news-category-filters">
              {newsCategories.map(category => (
                <button
                  key={category}
                  className={`news-category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Article */}
          {featuredArticle && (
            <div className="news-featured">
              <div className="news-featured-content">
                <div className="news-featured-image">
                  <img src={featuredArticle.image} alt={featuredArticle.title} />
                  <div className="news-featured-badge">Featured</div>
                </div>
                <div className="news-featured-info">
                  <div className="news-featured-meta">
                    <span className="news-category-tag">{featuredArticle.category}</span>
                    <span className="news-read-time">{featuredArticle.readTime}</span>
                  </div>
                  <h2 className="news-featured-title">{featuredArticle.title}</h2>
                  <p className="news-featured-excerpt">{featuredArticle.excerpt}</p>
                  <div className="news-featured-author">
                    <img src={featuredArticle.authorAvatar} alt={featuredArticle.author} className="news-author-avatar" />
                    <div className="news-author-info">
                      <span className="news-author-name">{featuredArticle.author}</span>
                      <span className="news-publish-date">{featuredArticle.publishedAt}</span>
                    </div>
                  </div>
                  <div className="news-featured-tags">
                    {featuredArticle.tags.map((tag, index) => (
                      <span key={index} className="news-tag">{tag}</span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Articles Grid */}
          <div className="news-grid">
            {regularArticles.map(article => (
              <article key={article.id} className="news-card">
                <div className="news-card-image">
                  <img src={article.image} alt={article.title} />
                  <div className="news-card-category">{article.category}</div>
                </div>
                <div className="news-card-content">
                  <div className="news-card-meta">
                    <span className="news-card-read-time">{article.readTime}</span>
                    <span className="news-card-date">{article.publishedAt}</span>
                  </div>
                  <h3 className="news-card-title">{article.title}</h3>
                  <p className="news-card-excerpt">{article.excerpt}</p>
                  <div className="news-card-author">
                    <img src={article.authorAvatar} alt={article.author} className="news-card-author-avatar" />
                    <span className="news-card-author-name">{article.author}</span>
                  </div>
                  <div className="news-card-tags">
                    {article.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="news-card-tag">{tag}</span>
                    ))}
                  </div>
                </div>
              </article>
            ))}
          </div>

          {filteredArticles.length === 0 && (
            <div className="news-no-results">
              <p>No articles found matching your search criteria.</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default NewsPage;

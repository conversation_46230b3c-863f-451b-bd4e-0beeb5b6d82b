import React, { useState } from 'react';
import './Podcast.css';

const SimplePodcast = () => {
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Sample podcast data
  const podcastData = [
    {
      id: 1,
      title: "Space Technology Today",
      description: "Exploring the latest developments in space technology and their impact on future missions.",
      thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
      duration: "45:32",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. <PERSON>",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
      },
      category: "Technology",
      listens: "12.5K"
    },
    {
      id: 2,
      title: "Mars Mission Updates",
      description: "Weekly updates on Mars missions, discoveries, and the latest developments in space exploration.",
      thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop&crop=center",
      duration: "38:15",
      publishedAt: "4 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Mike Chen",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center"
      },
      category: "Science",
      listens: "8.7K"
    },
    {
      id: 3,
      title: "Astronaut Life Stories",
      description: "Personal stories and experiences from astronauts who have lived and worked in space.",
      thumbnail: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=400&fit=crop&crop=center",
      duration: "52:18",
      publishedAt: "1 week ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Commander Lisa Park",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=center"
      },
      category: "Exploration",
      listens: "15.2K"
    }
  ];

  const categories = ['All', 'Technology', 'Science', 'Exploration', 'News'];

  const handlePlayPause = (episodeId) => {
    setCurrentlyPlaying(currentlyPlaying === episodeId ? null : episodeId);
  };

  const filteredEpisodes = podcastData.filter(episode => {
    const matchesSearch = episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         episode.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || episode.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <section className="podcast-section">
      <div className="podcast-container">
        <div className="podcast-header">
          <h2 className="podcast-title">Our Podcasts</h2>
          <button className="podcast-view-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        {/* Search and Filter Controls */}
        <div className="podcast-controls">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search podcasts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            <svg className="search-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </div>
          
          <div className="category-filters">
            {categories.map(category => (
              <button
                key={category}
                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Episode Grid */}
        <div className="podcast-grid">
          {filteredEpisodes.map((episode) => (
            <article key={episode.id} className="podcast-card">
              <div className="podcast-thumbnail">
                <img src={episode.thumbnail} alt={episode.title} />
                <button 
                  className="play-button"
                  onClick={() => handlePlayPause(episode.id)}
                >
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    {currentlyPlaying === episode.id ? (
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    ) : (
                      <path d="M8 5v14l11-7z"/>
                    )}
                  </svg>
                </button>
                <div className="episode-duration">{episode.duration}</div>
                <div className="category-badge">{episode.category}</div>
              </div>

              <div className="podcast-content">
                <h3 className="podcast-card-title">{episode.title}</h3>
                <p className="podcast-description">{episode.description}</p>
                
                <div className="podcast-meta">
                  <div className="host-info">
                    <img src={episode.host.avatar} alt={episode.host.name} className="host-avatar" />
                    <div className="host-details">
                      <span className="host-name">{episode.host.name}</span>
                      <div className="episode-stats">
                        <span className="listens">{episode.listens} listens</span>
                        <span className="publish-date">{episode.publishedAt}</span>
                      </div>
                    </div>
                  </div>
                  
                  <button className="listen-btn">
                    Listen Now
                    <span className="btn-arrow">→</span>
                  </button>
                </div>
              </div>

              {currentlyPlaying === episode.id && (
                <div className="audio-player-container">
                  <div className="simple-audio-player">
                    <audio controls src={episode.audioUrl}>
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                </div>
              )}
            </article>
          ))}
        </div>

        {filteredEpisodes.length === 0 && (
          <div className="no-results">
            <p>No podcasts found matching your search criteria.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default SimplePodcast;

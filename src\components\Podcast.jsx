import React, { useState, useEffect } from 'react';
import AudioPlayer from './AudioPlayer';
import './Podcast.css';

const Podcast = () => {
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [visibleCount, setVisibleCount] = useState(6);

  const podcastData = [
    {
      id: 1,
      title: "Today's Technology with <PERSON>",
      description: "On this podcast, I talk about what happened in technology. Exploring the latest developments in space technology and their impact on future missions.",
      thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
      duration: "45:32",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav", // Sample audio URL - replace with actual podcast URLs
      host: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
      },
      category: "Technology",
      featured: true,
      listens: "12.5K"
    },
    {
      id: 2,
      title: "Everyday Plant: The Plant and It Lifes",
      description: "Let's get to know more about plant and it lifes! Discover the fascinating world of space botany and how plants survive in zero gravity.",
      thumbnail: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=400&fit=crop&crop=center",
      duration: "38:15",
      publishedAt: "4 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Sarah Green",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center"
      },
      category: "Science",
      listens: "8.7K"
    },
    {
      id: 3,
      title: "Boy & Joy: Travel Through Wildlife",
      description: "With Boy, you will know the stories in the wildlife. Journey through space wildlife and the search for extraterrestrial life forms.",
      thumbnail: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=400&fit=crop&crop=center",
      duration: "52:08",
      publishedAt: "1 week ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Boy & Joy",
        avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=40&h=40&fit=crop&crop=center"
      },
      category: "Exploration",
      listens: "15.2K"
    },
    {
      id: 4,
      title: "Spacetronatus: Space and Universe Updates",
      description: "Spacetronatus: where we can know live updates in space. Get the latest news and discoveries from the cosmos and beyond.",
      thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop&crop=center",
      duration: "41:27",
      publishedAt: "1 week ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Alex Cosmos",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=center"
      },
      category: "News",
      listens: "9.8K"
    },
    {
      id: 5,
      title: "Whynauts: What's the Life of Astronauts?",
      description: "Let's dig deeper with my friends about the life of astronauts. Explore the daily challenges and triumphs of life in space.",
      thumbnail: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=400&fit=crop&crop=center",
      duration: "36:44",
      publishedAt: "2 weeks ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Captain Mike",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=center"
      },
      category: "Lifestyle",
      listens: "11.3K"
    },
    {
      id: 6,
      title: "Sonya Adventure: The Life of Traveler",
      description: "With Sonya, you will know the life of a backpacker. Adventures in space tourism and the future of civilian space travel.",
      thumbnail: "https://images.unsplash.com/photo-1502134249126-9f3755a50d78?w=400&h=400&fit=crop&crop=center",
      duration: "44:19",
      publishedAt: "2 weeks ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Sonya Explorer",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=center"
      },
      category: "Travel",
      listens: "7.9K"
    },
    {
      id: 7,
      title: "Extraterrestrial Life: Is It Exists?",
      description: "We'll dig deeper to get to know the truth of alien existence. Scientific exploration of the possibility of life beyond Earth.",
      thumbnail: "https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=400&h=400&fit=crop&crop=center",
      duration: "48:55",
      publishedAt: "3 weeks ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Elena Vega",
        avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=center"
      },
      category: "Science",
      listens: "18.6K"
    },
    {
      id: 8,
      title: "Earth Now: What Happens With Earth?",
      description: "Earth Now, where you can get updates of our planet. Climate change impacts and Earth observation from space satellites.",
      thumbnail: "https://images.unsplash.com/photo-*********1146-b6fa6a46bcb4?w=400&h=400&fit=crop&crop=center",
      duration: "39:12",
      publishedAt: "3 weeks ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. James Earth",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
      },
      category: "Environment",
      listens: "13.4K"
    },
    {
      id: 9,
      title: "Teeech: A Journey With Technology",
      description: "Newest updates through the technology and stuffs. How cutting-edge technology is revolutionizing space exploration and research.",
      thumbnail: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop&crop=center",
      duration: "42:33",
      publishedAt: "1 month ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Tech Guru",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=center"
      },
      category: "Technology",
      listens: "10.7K"
    }
  ];

  const categories = ['All', 'Technology', 'Science', 'Exploration', 'News', 'Lifestyle', 'Travel', 'Environment'];

  const handlePlayPause = (episodeId) => {
    setCurrentlyPlaying(currentlyPlaying === episodeId ? null : episodeId);
  };

  const filteredEpisodes = podcastData.filter(episode => {
    const matchesSearch = episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         episode.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         episode.host.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || episode.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredEpisode = filteredEpisodes.find(episode => episode.featured) || filteredEpisodes[0];
  const regularEpisodes = filteredEpisodes.filter(episode => !episode.featured);
  const visibleEpisodes = regularEpisodes.slice(0, visibleCount);
  const hasMoreEpisodes = regularEpisodes.length > visibleCount;

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 6);
  };

  // Reset visible count when search or category changes
  useEffect(() => {
    setVisibleCount(6);
  }, [searchTerm, selectedCategory]);

  return (
    <section className="podcast-section">
      <div className="podcast-container">
        <div className="podcast-header">
          <h2 className="podcast-title">Our Podcasts</h2>
          <button className="podcast-view-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        {/* Search and Filter Controls */}
        <div className="podcast-controls">
          <div className="search-container">
            <input
              type="text"
              placeholder="Search podcasts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
              aria-label="Search podcasts"
            />
            <svg className="search-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </div>

          <div className="category-filters">
            {categories.map(category => (
              <button
                key={category}
                className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Featured Episode */}
        {featuredEpisode && (
          <div className="featured-episode">
            <div className="featured-content">
              <div className="featured-info">
                <span className="featured-badge">Featured Episode</span>
                <h3 className="featured-title">{featuredEpisode.title}</h3>
                <p className="featured-description">{featuredEpisode.description}</p>

                <div className="featured-meta">
                  <div className="host-info">
                    <img src={featuredEpisode.host.avatar} alt={featuredEpisode.host.name} className="host-avatar" />
                    <div className="host-details">
                      <span className="host-name">{featuredEpisode.host.name}</span>
                      <div className="episode-stats">
                        <span className="duration">{featuredEpisode.duration}</span>
                        <span className="listens">{featuredEpisode.listens} listens</span>
                        <span className="publish-date">{featuredEpisode.publishedAt}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <AudioPlayer
                  episode={featuredEpisode}
                  isPlaying={currentlyPlaying === featuredEpisode.id}
                  onPlayPause={() => handlePlayPause(featuredEpisode.id)}
                />
              </div>

              <div className="featured-thumbnail">
                <img src={featuredEpisode.thumbnail} alt={featuredEpisode.title} />
                <div className="category-badge">{featuredEpisode.category}</div>
              </div>
            </div>
          </div>
        )}

        {/* Episode Grid */}
        <div className="podcast-grid">
          {visibleEpisodes.map((episode) => (
            <article key={episode.id} className="podcast-card">
              <div className="podcast-thumbnail">
                <img src={episode.thumbnail} alt={episode.title} />
                <button
                  className="play-button"
                  onClick={() => handlePlayPause(episode.id)}
                  aria-label={`${currentlyPlaying === episode.id ? 'Pause' : 'Play'} ${episode.title}`}
                >
                  <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                    {currentlyPlaying === episode.id ? (
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    ) : (
                      <path d="M8 5v14l11-7z"/>
                    )}
                  </svg>
                </button>
                <div className="episode-duration">{episode.duration}</div>
                <div className="category-badge">{episode.category}</div>
              </div>

              <div className="podcast-content">
                <h3 className="podcast-card-title">{episode.title}</h3>
                <p className="podcast-description">{episode.description}</p>

                <div className="podcast-meta">
                  <div className="host-info">
                    <img src={episode.host.avatar} alt={episode.host.name} className="host-avatar" />
                    <div className="host-details">
                      <span className="host-name">{episode.host.name}</span>
                      <div className="episode-stats">
                        <span className="listens">{episode.listens} listens</span>
                        <span className="publish-date">{episode.publishedAt}</span>
                      </div>
                    </div>
                  </div>

                  <button className="listen-btn">
                    Listen Now
                    <span className="btn-arrow">→</span>
                  </button>
                </div>
              </div>

              {currentlyPlaying === episode.id && (
                <div className="audio-player-container">
                  <AudioPlayer
                    episode={episode}
                    isPlaying={true}
                    onPlayPause={() => handlePlayPause(episode.id)}
                  />
                </div>
              )}
            </article>
          ))}
        </div>

        {/* Load More Button */}
        {hasMoreEpisodes && (
          <div className="load-more-container">
            <button className="load-more-btn" onClick={handleLoadMore}>
              Load More Episodes
              <span className="btn-arrow">↓</span>
            </button>
          </div>
        )}

        {filteredEpisodes.length === 0 && (
          <div className="no-results">
            <p>No podcasts found matching your search criteria.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default Podcast;

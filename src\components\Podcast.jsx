import React, { useState, useEffect, useCallback } from 'react';
import AudioPlayer from './AudioPlayer';
import { getSpaceRelatedEpisodes, searchEpisodes, transformEpisodeData } from '../services/podcastApi';
import './Podcast.css';

const Podcast = () => {
  const [currentlyPlaying, setCurrentlyPlaying] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [visibleCount, setVisibleCount] = useState(6);
  const [podcastData, setPodcastData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fallback data for when API fails
  const getFallbackData = () => [
    {
      id: 1,
      title: "Today's Technology with <PERSON>",
      description: "On this podcast, I talk about what happened in technology. Exploring the latest developments in space technology and their impact on future missions.",
      thumbnail: "https://images.unsplash.com/photo-1614728263952-84ea256f9679?w=400&h=400&fit=crop&crop=center",
      duration: "45:32",
      publishedAt: "2 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Danny Reighman",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=center"
      },
      category: "Technology",
      featured: true,
      listens: "12.5K"
    },
    {
      id: 2,
      title: "Space Exploration Weekly",
      description: "Weekly updates on space missions, discoveries, and the latest developments in space technology.",
      thumbnail: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=400&fit=crop&crop=center",
      duration: "38:15",
      publishedAt: "4 days ago",
      audioUrl: "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
      host: {
        name: "Dr. Sarah Green",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=center"
      },
      category: "Science",
      listens: "8.7K"
    }
  ];

  // Load initial podcast data
  const loadPodcastData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const episodes = await getSpaceRelatedEpisodes();
      setPodcastData(episodes);
    } catch (err) {
      console.error('Failed to load podcast data:', err);
      setError('Failed to load podcast episodes. Please try again later.');
      setPodcastData(getFallbackData());
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPodcastData();
  }, [loadPodcastData]);

  // Handle search with API
  const handleSearch = useCallback(async (query) => {
    if (!query.trim()) {
      loadPodcastData();
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const episodes = await searchEpisodes(query, { len_min: 10, len_max: 120 });
      const transformedEpisodes = episodes.map((episode, index) => transformEpisodeData(episode, index));
      setPodcastData(transformedEpisodes);
    } catch (err) {
      console.error('Search failed:', err);
      setError('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [loadPodcastData]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm) {
        handleSearch(searchTerm);
      } else {
        loadPodcastData();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, handleSearch, loadPodcastData]);

  // Reset visible count when search or category changes
  useEffect(() => {
    setVisibleCount(6);
  }, [searchTerm, selectedCategory]);

  const categories = ['All', 'Technology', 'Science', 'Exploration', 'News', 'Lifestyle', 'Travel', 'Environment'];

  const handlePlayPause = (episodeId) => {
    setCurrentlyPlaying(currentlyPlaying === episodeId ? null : episodeId);
  };

  const filteredEpisodes = podcastData.filter(episode => {
    const matchesSearch = episode.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         episode.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         episode.host.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || episode.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredEpisode = filteredEpisodes.find(episode => episode.featured) || filteredEpisodes[0];
  const regularEpisodes = filteredEpisodes.filter(episode => !episode.featured);
  const visibleEpisodes = regularEpisodes.slice(0, visibleCount);
  const hasMoreEpisodes = regularEpisodes.length > visibleCount;

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 6);
  };

  return (
    <section className="podcast-section">
      <div className="podcast-container">
        <div className="podcast-header">
          <h2 className="podcast-title">Our Podcasts</h2>
          <button className="podcast-view-all">
            see all <span className="arrow">→</span>
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading podcast episodes...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="error-container">
            <p className="error-message">{error}</p>
            <button className="retry-btn" onClick={loadPodcastData}>
              Try Again
            </button>
          </div>
        )}

        {/* Search and Filter Controls */}
        {!loading && (
          <div className="podcast-controls">
            <div className="search-container">
              <input
                type="text"
                placeholder="Search podcasts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
                aria-label="Search podcasts"
              />
              <svg className="search-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
            </div>
            
            <div className="category-filters">
              {categories.map(category => (
                <button
                  key={category}
                  className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Featured Episode */}
        {!loading && featuredEpisode && (
          <div className="featured-episode">
            <div className="featured-content">
              <div className="featured-info">
                <span className="featured-badge">Featured Episode</span>
                <h3 className="featured-title">{featuredEpisode.title}</h3>
                <p className="featured-description">{featuredEpisode.description}</p>
                
                <div className="featured-meta">
                  <div className="host-info">
                    <img src={featuredEpisode.host.avatar} alt={featuredEpisode.host.name} className="host-avatar" />
                    <div className="host-details">
                      <span className="host-name">{featuredEpisode.host.name}</span>
                      <div className="episode-stats">
                        <span className="duration">{featuredEpisode.duration}</span>
                        <span className="listens">{featuredEpisode.listens} listens</span>
                        <span className="publish-date">{featuredEpisode.publishedAt}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <AudioPlayer
                  episode={featuredEpisode}
                  isPlaying={currentlyPlaying === featuredEpisode.id}
                  onPlayPause={() => handlePlayPause(featuredEpisode.id)}
                />
              </div>
              
              <div className="featured-thumbnail">
                <img src={featuredEpisode.thumbnail} alt={featuredEpisode.title} />
                <div className="category-badge">{featuredEpisode.category}</div>
              </div>
            </div>
          </div>
        )}

        {/* Episode Grid */}
        {!loading && (
          <div className="podcast-grid">
            {visibleEpisodes.map((episode) => (
              <article key={episode.id} className="podcast-card">
                <div className="podcast-thumbnail">
                  <img src={episode.thumbnail} alt={episode.title} />
                  <button 
                    className="play-button"
                    onClick={() => handlePlayPause(episode.id)}
                    aria-label={`${currentlyPlaying === episode.id ? 'Pause' : 'Play'} ${episode.title}`}
                  >
                    <svg className="play-icon" viewBox="0 0 24 24" fill="currentColor">
                      {currentlyPlaying === episode.id ? (
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                      ) : (
                        <path d="M8 5v14l11-7z"/>
                      )}
                    </svg>
                  </button>
                  <div className="episode-duration">{episode.duration}</div>
                  <div className="category-badge">{episode.category}</div>
                </div>

                <div className="podcast-content">
                  <h3 className="podcast-card-title">{episode.title}</h3>
                  <p className="podcast-description">{episode.description}</p>
                  
                  <div className="podcast-meta">
                    <div className="host-info">
                      <img src={episode.host.avatar} alt={episode.host.name} className="host-avatar" />
                      <div className="host-details">
                        <span className="host-name">{episode.host.name}</span>
                        <div className="episode-stats">
                          <span className="listens">{episode.listens} listens</span>
                          <span className="publish-date">{episode.publishedAt}</span>
                        </div>
                      </div>
                    </div>
                    
                    <button className="listen-btn">
                      Listen Now
                      <span className="btn-arrow">→</span>
                    </button>
                  </div>
                </div>

                {currentlyPlaying === episode.id && (
                  <div className="audio-player-container">
                    <AudioPlayer
                      episode={episode}
                      isPlaying={true}
                      onPlayPause={() => handlePlayPause(episode.id)}
                    />
                  </div>
                )}
              </article>
            ))}
          </div>
        )}

        {/* Load More Button */}
        {!loading && hasMoreEpisodes && (
          <div className="load-more-container">
            <button className="load-more-btn" onClick={handleLoadMore}>
              Load More Episodes
              <span className="btn-arrow">↓</span>
            </button>
          </div>
        )}

        {!loading && filteredEpisodes.length === 0 && (
          <div className="no-results">
            <p>No podcasts found matching your search criteria.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default Podcast;

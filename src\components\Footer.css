/* Footer Component - Space Theme */
.footer {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 4rem 0 2rem;
  overflow: hidden;
}



.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  /* padding: 0 2rem; */
  position: relative;
  z-index: 2;
}

/* Header Section */
.footer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.footer-logo {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a5b4fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
  transform: translateY(-2px);
}

.social-svg {
  width: 20px;
  height: 20px;
  color: #b3b3b3;
  transition: color 0.3s ease;
}

.social-link:hover .social-svg {
  color: #ffffff;
}

/* Newsletter Section */
.footer-newsletter {
  max-width: 400px;
}

.newsletter-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #d1d5db;
}

.newsletter-form {
  width: 100%;
}

.newsletter-input-container {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.newsletter-input-container:focus-within {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.newsletter-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 0.9rem;
  outline: none;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.newsletter-submit {
  background: rgba(79, 70, 229, 0.8);
  border: none;
  padding: 0.75rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter-submit:hover {
  background: rgba(79, 70, 229, 1);
}

.submit-icon {
  width: 20px;
  height: 20px;
}

/* Links Section */
.footer-links {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.footer-column {
  display: flex;
  flex-direction: column;
}

.footer-column-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #ffffff;
}

.footer-column-links {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-link {
  color: #b3b3b3;
  text-decoration: none;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
}

.footer-link:hover {
  color: #a5b4fc;
  transform: translateX(3px);
}

/* Bottom Section */
.footer-bottom {
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright {
  text-align: center;
}

.footer-copyright p {
  font-size: 0.8rem;
  color: #888;
  line-height: 1.5;
  margin: 0;
}

.legal-link {
  color: #a5b4fc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.legal-link:hover {
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-links {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 3rem 0 2rem;
  }

  .footer-container {
    padding: 0 1rem;
  }

  .footer-header {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .footer-newsletter {
    max-width: 100%;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .footer-social {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-links {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .footer-column {
    text-align: center;
  }

  .newsletter-input-container {
    flex-direction: column;
  }

  .newsletter-submit {
    border-radius: 0 0 12px 12px;
  }

  .footer-copyright p {
    font-size: 0.75rem;
  }
}

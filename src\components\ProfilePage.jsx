import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import './ProfilePage.css';

const ProfilePage = () => {
  const { user, updateProfile, logout, defaultAvatars, loading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '',
    email: user?.email || '',
    avatar: user?.avatar || '',
    preferences: {
      newsletter: user?.preferences?.newsletter || true,
      notifications: user?.preferences?.notifications || true,
      theme: user?.preferences?.theme || 'space'
    }
  });
  const [errors, setErrors] = useState({});
  const [successMessage, setSuccessMessage] = useState('');
  const [selectedAvatar, setSelectedAvatar] = useState(user?.avatar || '');
  const [customAvatar, setCustomAvatar] = useState('');

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.startsWith('preferences.')) {
      const prefKey = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          [prefKey]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({ 
        ...prev, 
        [name]: type === 'checkbox' ? checked : value 
      }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleAvatarSelect = (avatar) => {
    setSelectedAvatar(avatar);
    setFormData(prev => ({ ...prev, avatar }));
    setCustomAvatar('');
  };

  const handleCustomAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const avatarUrl = e.target.result;
        setCustomAvatar(avatarUrl);
        setFormData(prev => ({ ...prev, avatar: avatarUrl }));
        setSelectedAvatar('');
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.displayName) {
      newErrors.displayName = 'Display name is required';
    } else if (formData.displayName.length < 2) {
      newErrors.displayName = 'Display name must be at least 2 characters long';
    }

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await updateProfile(formData);
      setIsEditing(false);
      setSuccessMessage('Profile updated successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      setErrors({ submit: error.message });
    }
  };

  const handleCancel = () => {
    setFormData({
      displayName: user?.displayName || '',
      email: user?.email || '',
      avatar: user?.avatar || '',
      preferences: {
        newsletter: user?.preferences?.newsletter || true,
        notifications: user?.preferences?.notifications || true,
        theme: user?.preferences?.theme || 'space'
      }
    });
    setSelectedAvatar(user?.avatar || '');
    setCustomAvatar('');
    setIsEditing(false);
    setErrors({});
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <div className="profile-page">
        <div className="profile-container">
          <div className="profile-error">
            <h2>Access Denied</h2>
            <p>Please log in to view your profile.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="profile-page">
      <div className="profile-container">
        {/* Profile Header */}
        <div className="profile-header">
          <div className="profile-avatar-section">
            <div className="profile-avatar-large">
              <img src={user.avatar} alt={user.displayName} />
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{user.displayName}</h1>
              <p className="profile-email">{user.email}</p>
              <p className="profile-joined">Joined {formatDate(user.createdAt)}</p>
            </div>
          </div>
          <div className="profile-actions">
            {!isEditing ? (
              <button 
                className="profile-edit-btn"
                onClick={() => setIsEditing(true)}
              >
                <svg viewBox="0 0 24 24" fill="currentColor" className="edit-icon">
                  <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                Edit Profile
              </button>
            ) : (
              <div className="profile-edit-actions">
                <button 
                  className="profile-save-btn"
                  onClick={handleSave}
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save Changes'}
                </button>
                <button 
                  className="profile-cancel-btn"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="profile-success-banner">
            {successMessage}
          </div>
        )}

        {/* Error Message */}
        {errors.submit && (
          <div className="profile-error-banner">
            {errors.submit}
          </div>
        )}

        {/* Profile Content */}
        <div className="profile-content">
          {/* Personal Information */}
          <div className="profile-section">
            <h2 className="section-title">Personal Information</h2>
            <div className="profile-form">
              {/* Avatar Selection (only in edit mode) */}
              {isEditing && (
                <div className="form-group">
                  <label className="form-label">Profile Picture</label>
                  <div className="avatar-selection">
                    <div className="default-avatars">
                      {defaultAvatars.map((avatar, index) => (
                        <button
                          key={index}
                          type="button"
                          className={`avatar-option ${selectedAvatar === avatar ? 'selected' : ''}`}
                          onClick={() => handleAvatarSelect(avatar)}
                        >
                          <img src={avatar} alt={`Avatar ${index + 1}`} />
                        </button>
                      ))}
                    </div>
                    <div className="custom-avatar-upload">
                      <input
                        type="file"
                        id="customAvatar"
                        accept="image/*"
                        onChange={handleCustomAvatarChange}
                        className="avatar-input"
                      />
                      <label htmlFor="customAvatar" className="avatar-upload-btn">
                        <svg viewBox="0 0 24 24" fill="currentColor" className="upload-icon">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        Upload Custom
                      </label>
                      {customAvatar && (
                        <div className="custom-avatar-preview">
                          <img src={customAvatar} alt="Custom avatar" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Display Name */}
              <div className="form-group">
                <label htmlFor="displayName" className="form-label">Display Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    id="displayName"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleInputChange}
                    className={`form-input ${errors.displayName ? 'error' : ''}`}
                    placeholder="Enter your display name"
                  />
                ) : (
                  <div className="form-value">{user.displayName}</div>
                )}
                {errors.displayName && <span className="form-error">{errors.displayName}</span>}
              </div>

              {/* Email */}
              <div className="form-group">
                <label htmlFor="email" className="form-label">Email Address</label>
                {isEditing ? (
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`form-input ${errors.email ? 'error' : ''}`}
                    placeholder="Enter your email address"
                  />
                ) : (
                  <div className="form-value">{user.email}</div>
                )}
                {errors.email && <span className="form-error">{errors.email}</span>}
              </div>
            </div>
          </div>

          {/* Preferences */}
          <div className="profile-section">
            <h2 className="section-title">Preferences</h2>
            <div className="preferences-grid">
              <div className="preference-item">
                <label className="preference-label">
                  <input
                    type="checkbox"
                    name="preferences.newsletter"
                    checked={formData.preferences.newsletter}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="preference-checkbox"
                  />
                  <span className="preference-text">
                    <strong>Newsletter Subscription</strong>
                    <span className="preference-description">
                      Receive weekly updates about space discoveries and news
                    </span>
                  </span>
                </label>
              </div>

              <div className="preference-item">
                <label className="preference-label">
                  <input
                    type="checkbox"
                    name="preferences.notifications"
                    checked={formData.preferences.notifications}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="preference-checkbox"
                  />
                  <span className="preference-text">
                    <strong>Push Notifications</strong>
                    <span className="preference-description">
                      Get notified about breaking space news and updates
                    </span>
                  </span>
                </label>
              </div>

              <div className="preference-item">
                <label className="preference-label">
                  <span className="preference-text">
                    <strong>Theme Preference</strong>
                    <span className="preference-description">
                      Choose your preferred website theme
                    </span>
                  </span>
                </label>
                {isEditing ? (
                  <select
                    name="preferences.theme"
                    value={formData.preferences.theme}
                    onChange={handleInputChange}
                    className="preference-select"
                  >
                    <option value="space">Space Theme</option>
                    <option value="dark">Dark Theme</option>
                    <option value="light">Light Theme</option>
                  </select>
                ) : (
                  <div className="preference-value">
                    {formData.preferences.theme === 'space' ? 'Space Theme' : 
                     formData.preferences.theme === 'dark' ? 'Dark Theme' : 'Light Theme'}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Account Actions */}
          <div className="profile-section">
            <h2 className="section-title">Account Actions</h2>
            <div className="account-actions">
              <button 
                className="logout-btn"
                onClick={logout}
              >
                <svg viewBox="0 0 24 24" fill="currentColor" className="logout-icon">
                  <path d="M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H4v16h10v-2h2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h10z"/>
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;

/* Podcast Section Styles */
.podcast-section {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.podcast-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.podcast-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.podcast-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
}

.podcast-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a8a8a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.podcast-view-all {
  background: transparent;
  border: none;
  color: #b3b3b3;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
}

.podcast-view-all:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.arrow {
  transition: transform 0.3s ease;
}

.podcast-view-all:hover .arrow {
  transform: translateX(3px);
}

/* Search and Filter Controls */
.podcast-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.search-container {
  position: relative;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.search-input:focus {
  outline: none;
  border-color: rgba(79, 70, 229, 0.5);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
}

.search-input::placeholder {
  color: #888;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  color: #888;
  pointer-events: none;
}

.category-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.category-btn {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  color: #b3b3b3;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.category-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.category-btn.active {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-color: rgba(79, 70, 229, 0.5);
  color: white;
}

/* Featured Episode */
.featured-episode {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 3rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.featured-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  padding: 2rem;
}

.featured-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.featured-badge {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  align-self: flex-start;
}

.featured-title {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 1.3;
  margin: 0;
  color: white;
}

.featured-description {
  color: #b3b3b3;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.featured-thumbnail {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
}

.featured-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  aspect-ratio: 1;
}

/* Podcast Grid */
.podcast-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.podcast-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  cursor: pointer;
}

.podcast-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(79, 70, 229, 0.3);
}

.podcast-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.podcast-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.podcast-card:hover .podcast-thumbnail img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(79, 70, 229, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  opacity: 0;
}

.podcast-thumbnail:hover .play-button {
  opacity: 1;
}

.play-button:hover {
  background: rgba(79, 70, 229, 1);
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.6);
}

.play-icon {
  width: 24px;
  height: 24px;
  color: #ffffff;
  margin-left: 2px;
}

.episode-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.category-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.podcast-content {
  padding: 1.5rem;
}

.podcast-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 1rem 0;
  color: white;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.podcast-description {
  color: #b3b3b3;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.podcast-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.host-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.host-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.host-avatar:hover {
  border-color: rgba(79, 70, 229, 0.5);
  transform: scale(1.1);
}

.host-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.host-name {
  color: #ffffff;
  font-size: 0.85rem;
  font-weight: 500;
}

.episode-stats {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #888;
}

.episode-stats span:not(:last-child)::after {
  content: '•';
  margin-left: 0.5rem;
  color: #666;
}

.listen-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.listen-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.listen-btn:hover .btn-arrow {
  transform: translateX(3px);
}

.audio-player-container {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.simple-audio-player {
  width: 100%;
}

.simple-audio-player audio {
  width: 100%;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  outline: none;
}

.simple-audio-player audio::-webkit-media-controls-panel {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.simple-audio-player audio::-webkit-media-controls-play-button,
.simple-audio-player audio::-webkit-media-controls-pause-button {
  background-color: #4f46e5;
  border-radius: 50%;
}

.simple-audio-player audio::-webkit-media-controls-timeline {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.simple-audio-player audio::-webkit-media-controls-current-time-display,
.simple-audio-player audio::-webkit-media-controls-time-remaining-display {
  color: white;
  font-size: 12px;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
}

.load-more-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.load-more-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(79, 70, 229, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.load-more-btn .btn-arrow {
  transition: transform 0.3s ease;
}

.load-more-btn:hover .btn-arrow {
  transform: translateY(3px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #b3b3b3;
  font-size: 1.1rem;
  margin: 0;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  margin-bottom: 2rem;
}

.error-message {
  color: #fca5a5;
  font-size: 1rem;
  margin: 0 0 1rem 0;
}

.retry-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: #888;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .podcast-section {
    padding: 4rem 0;
  }

  .podcast-container {
    padding: 0 1rem;
  }

  .podcast-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  .podcast-title {
    font-size: 2rem;
  }

  .podcast-controls {
    gap: 1rem;
  }

  .category-filters {
    justify-content: center;
  }

  .featured-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .featured-thumbnail {
    order: -1;
    max-width: 200px;
    margin: 0 auto;
  }

  .podcast-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .podcast-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .listen-btn {
    align-self: stretch;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .podcast-title {
    font-size: 1.75rem;
  }

  .podcast-content {
    padding: 1rem;
  }

  .featured-content {
    padding: 1rem;
  }

  .featured-title {
    font-size: 1.4rem;
  }

  .podcast-thumbnail {
    height: 180px;
  }

  .host-avatar {
    width: 28px;
    height: 28px;
  }

  .host-name {
    font-size: 0.8rem;
  }

  .episode-stats {
    font-size: 0.7rem;
  }
}

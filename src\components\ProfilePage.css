/* Profile Page - Space Theme */
.profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 70px 0 2rem;
  position: relative;
  overflow: hidden;
}

.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.1), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.08), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.06), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  pointer-events: none;
}

.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Profile Header */
.profile-header {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 2rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.profile-avatar-large {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
}

.profile-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #a5b4fc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-email {
  font-size: 1.1rem;
  color: #b3b3b3;
  margin: 0;
}

.profile-joined {
  font-size: 0.9rem;
  color: #888;
  margin: 0;
}

.profile-actions {
  display: flex;
  gap: 1rem;
}

.profile-edit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-edit-btn:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

.edit-icon {
  width: 16px;
  height: 16px;
}

.profile-edit-actions {
  display: flex;
  gap: 0.75rem;
}

.profile-save-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-save-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
}

.profile-save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.profile-cancel-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #d1d5db;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-cancel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Success and Error Banners */
.profile-success-banner,
.profile-error-banner {
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  text-align: center;
}

.profile-success-banner {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #6ee7b7;
}

.profile-error-banner {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

/* Profile Content */
.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  color: #a5b4fc;
}

/* Form Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #d1d5db;
}

.form-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.form-input:focus {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input.error {
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-value {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: #d1d5db;
  font-size: 1rem;
}

.form-error {
  color: #fca5a5;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Avatar Selection */
.avatar-selection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.default-avatars {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.avatar-option {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.avatar-option img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-option:hover,
.avatar-option.selected {
  border-color: rgba(79, 70, 229, 0.8);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
  transform: scale(1.05);
}

.custom-avatar-upload {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.avatar-input {
  display: none;
}

.avatar-upload-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(79, 70, 229, 0.2);
  border: 1px solid rgba(79, 70, 229, 0.3);
  color: #a5b4fc;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.avatar-upload-btn:hover {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
}

.upload-icon {
  width: 16px;
  height: 16px;
}

.custom-avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid rgba(79, 70, 229, 0.8);
}

.custom-avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Preferences */
.preferences-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preference-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.preference-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.preference-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
}

.preference-checkbox {
  width: 20px;
  height: 20px;
  accent-color: #4f46e5;
  cursor: pointer;
  margin-top: 2px;
}

.preference-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.preference-text strong {
  color: white;
  font-size: 1rem;
}

.preference-description {
  color: #b3b3b3;
  font-size: 0.9rem;
  line-height: 1.4;
}

.preference-select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  color: white;
  font-size: 0.9rem;
  outline: none;
  cursor: pointer;
  margin-top: 0.5rem;
}

.preference-select:focus {
  border-color: rgba(79, 70, 229, 0.5);
}

.preference-value {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  color: #d1d5db;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* Account Actions */
.account-actions {
  display: flex;
  gap: 1rem;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  color: #fca5a5;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
  color: white;
}

.logout-icon {
  width: 16px;
  height: 16px;
}

/* Error State */
.profile-error {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-error h2 {
  color: #fca5a5;
  margin-bottom: 1rem;
}

.profile-error p {
  color: #b3b3b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 0 1rem;
  }

  .profile-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .profile-name {
    font-size: 1.8rem;
  }

  .profile-edit-actions {
    flex-direction: column;
    width: 100%;
  }

  .default-avatars {
    justify-content: center;
  }

  .custom-avatar-upload {
    justify-content: center;
  }

  .account-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .profile-section {
    padding: 1.5rem;
  }

  .profile-header {
    padding: 1.5rem;
  }

  .profile-avatar-large {
    width: 80px;
    height: 80px;
  }

  .profile-name {
    font-size: 1.5rem;
  }

  .avatar-option {
    width: 50px;
    height: 50px;
  }

  .custom-avatar-upload {
    flex-direction: column;
    gap: 0.5rem;
  }

  .preference-item {
    padding: 1rem;
  }

  .preference-label {
    gap: 0.75rem;
  }
}
